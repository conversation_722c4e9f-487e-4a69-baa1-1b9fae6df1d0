22:03:18 - INFO - 🔧 小显存GPU检测，优化配置: batch_size=2, tile_size=3072
22:03:18 - INFO - === 开始高性能图像配准 ===
22:03:18 - INFO - 使用线程数: 16
22:03:18 - INFO - GPU加速: 启用
22:03:18 - INFO - 📁 加载数据中...
22:03:18 - INFO - 📷 发现 224 张图像
22:03:18 - INFO - 📐 加载 96 个理论位置
22:03:18 - INFO - 🔍 扫描区域: 33048.0 x 9216.0
22:03:18 - INFO - 🔍 寻找空间相邻图像对...
22:03:18 - INFO - 📏 图像尺寸: (2048, 2448)
22:03:18 - INFO - 🔗 发现 170 个相邻图像对
22:03:18 - INFO - ⚡ 开始超高速位移测量 - 使用 16 个线程
22:03:18 - INFO - 🔄 预加载 96 张图像...
22:03:31 - INFO - ✅ 成功测量 170 个位移
22:03:31 - INFO - 💾 保存配准结果到: TileConfiguration.registered_python_improved.txt
22:03:31 - INFO - ✅ 配准结果已保存: image_55\TileConfiguration.registered_python_improved.txt
22:03:31 - INFO - 📊 成功配准 96 个位置
22:03:31 - INFO - === 配准完成 - 总耗时: 12.80秒 ===
22:03:31 - INFO - === 开始高性能图像拼接 ===
22:03:31 - INFO - 画布大小: (11361, 35453, 3), 偏移: (np.float64(-52.88081860703176), np.float64(-52.698512220106))
22:03:31 - INFO - 预估内存需求: 12.23 GB
22:03:31 - INFO - 🖥️  GPU内存: 需要 6260.7MB, 可用 4095.6MB
22:03:31 - WARNING - ⚠️  GPU内存不足，使用分块处理
22:03:31 - INFO - 📏 分块大小计算: 可用内存=4095.6MB, 最大分块=13610, 选择=4096
22:03:31 - INFO - 📊 预期分块数量: 27 (3x9) - 大幅减少分块数量
22:03:31 - INFO - 🧩 使用分块大小: 4096x4096
22:03:31 - INFO - 📊 总分块数: 27 (3x9)
