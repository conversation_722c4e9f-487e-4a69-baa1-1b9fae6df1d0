import cv2
import numpy as np
import os
from scipy.optimize import least_squares
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from tqdm import tqdm

import torch
import torch.nn.functional as F

print(f"PyTorch可用: {torch.__version__}")
if torch.cuda.is_available():
    print(f"CUDA可用: {torch.cuda.get_device_name(0)}")
    print(f"CUDA内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
else:
    print("CUDA不可用，将使用CPU模式")


class GridStitcher:
    """
    高性能图像拼接器 - 基于相位相关和全局优化
    
    核心技术：
    1. 相位相关算法进行亚像素精度配准
    2. 全局优化消除累积误差
    3. Linear blending实现无缝融合
    4. 多线程并行处理提升性能
    """
    
    def __init__(self, image_dir, rows=3, cols=3, overlap_ratio=0.1, num_threads=16):
        """
        初始化拼接器
        
        Args:
            image_dir: 图像文件夹路径
            rows: 网格行数
            cols: 网格列数  
            overlap_ratio: 预估重叠度
            num_threads: 并行线程数
        """
        self.image_dir = image_dir
        self.rows = rows
        self.cols = cols
        self.overlap_ratio = overlap_ratio
        self.num_threads = num_threads
        
        # 数据存储
        self.color_images = {}      # 彩色图像（用于最终融合）
        self.gray_images = {}       # 灰度图像（用于相位相关计算）
        self.pairwise_offsets = {}  # 相邻图像偏移量
        self.final_positions = {}   # 全局优化后的位置
        
        # GPU融合配置 - 针对RTX 3050 4GB优化
        self.use_gpu = True
        self.gpu_memory_limit_gb = 3.8
        self.gpu_chunk_size = 8192        # 介于8192和12000之间
        self.memory_limit_gb = 30
        self.max_feather_pixels = 50
        self.force_gpu_chunked = True   # 强制使用GPU分块模式
        
        print(f"🚀 初始化高性能图像拼接器")
        print(f"   📁 目录: {image_dir}")
        print(f"   🎯 网格: {rows}×{cols} = {rows*cols} 张图像")
        print(f"   🧵 线程: {num_threads}")
        print(f"   💾 内存: 32GB 优化")

    # ========================= 第一阶段：图像加载 =========================
    
    def load_images_parallel(self):
        """
        第一阶段：并行加载所有图像
        
        技术要点：
        - 同时加载彩色和灰度版本
        - 彩色图用于最终融合，灰度图用于相位相关
        - 多线程并行加载提升IO性能
        """
        print(f"\n📋 第一阶段：并行加载图像")
        print(f"   🧵 使用 {self.num_threads} 个线程并行加载")
        
        def load_single_image(r, c):
            """加载单张图像的工作函数"""
            filename = f"r{r:03d}_c{c:03d}.jpg"
            filepath = os.path.join(self.image_dir, filename)
            
            if os.path.exists(filepath):
                # 加载彩色图像
                color_img = cv2.imread(filepath, cv2.IMREAD_COLOR)
                if color_img is not None:
                    # 创建灰度版本用于相位相关
                    gray_img = cv2.cvtColor(color_img, cv2.COLOR_BGR2GRAY)
                    return (r, c), color_img, gray_img, True
            
            return (r, c), None, None, False
        
        start_time = time.time()
        tasks = [(r, c) for r in range(self.rows) for c in range(self.cols)]
        
        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            future_to_pos = {executor.submit(load_single_image, r, c): (r, c) for r, c in tasks}
            
            with tqdm(total=len(tasks), desc="🖼️  加载图像", unit="张") as pbar:
                for future in as_completed(future_to_pos):
                    (r, c), color_img, gray_img, success = future.result()
                    
                    if success:
                        self.color_images[(r, c)] = color_img
                        self.gray_images[(r, c)] = gray_img
                    
                    pbar.set_postfix({'已加载': len(self.color_images)})
                    pbar.update(1)
        
        load_time = time.time() - start_time
        print(f"   ✅ 加载完成：{len(self.color_images)} 张图像，耗时 {load_time:.2f}s")
        
        if not self.color_images:
            raise ValueError("❌ 未找到任何图像文件！")

    # ========================= 第二阶段：相位相关配准 =========================
    
    def calculate_phase_correlation(self, img1, img2):
        """
        核心算法：增强版相位相关
        
        原理：
        1. 将图像转换到频域进行匹配
        2. 利用相位信息计算精确偏移
        3. 亚像素精度的峰值检测
        
        优势：
        - 对光照变化不敏感
        - 亚像素精度
        - 计算效率高
        """
        def apply_hanning_window(img):
            """应用汉宁窗减少边界效应"""
            h, w = img.shape
            hann_h = np.hanning(h).reshape(-1, 1)
            hann_w = np.hanning(w).reshape(1, -1)
            hann_2d = hann_h * hann_w
            return img.astype(np.float32) * hann_2d
        
        def subpixel_peak_refinement(correlation_map):
            """亚像素峰值精化 - 抛物线拟合"""
            peak_y, peak_x = np.unravel_index(np.argmax(correlation_map), correlation_map.shape)
            h, w = correlation_map.shape
            
            # 边界检查
            if peak_x == 0 or peak_x == w-1 or peak_y == 0 or peak_y == h-1:
                return peak_x, peak_y
            
            # X方向亚像素精化
            c1 = correlation_map[peak_y, peak_x-1]
            c2 = correlation_map[peak_y, peak_x]
            c3 = correlation_map[peak_y, peak_x+1]
            dx = 0.5 * (c1 - c3) / (c1 + c3 - 2*c2) if (c1 + c3 - 2*c2) != 0 else 0
            
            # Y方向亚像素精化  
            c1 = correlation_map[peak_y-1, peak_x]
            c2 = correlation_map[peak_y, peak_x]
            c3 = correlation_map[peak_y+1, peak_x]
            dy = 0.5 * (c1 - c3) / (c1 + c3 - 2*c2) if (c1 + c3 - 2*c2) != 0 else 0
            
            return peak_x + dx, peak_y + dy
        
        # 确保图像尺寸一致
        h = min(img1.shape[0], img2.shape[0])
        w = min(img1.shape[1], img2.shape[1])
        img1_crop = img1[:h, :w]
        img2_crop = img2[:h, :w]
        
        # 应用汉宁窗
        img1_windowed = apply_hanning_window(img1_crop)
        img2_windowed = apply_hanning_window(img2_crop)
        
        # 相位相关核心计算
        f1 = np.fft.fft2(img1_windowed)
        f2 = np.fft.fft2(img2_windowed)
        
        # 交叉功率谱
        cross_power = f1 * np.conj(f2)
        cross_power /= (np.abs(cross_power) + 1e-10)
        
        # 相位相关函数
        correlation = np.real(np.fft.ifft2(cross_power))
        
        # 亚像素峰值检测
        peak_x_sub, peak_y_sub = subpixel_peak_refinement(correlation)
        
        # 处理周期性偏移
        if peak_y_sub > h // 2:
            peak_y_sub -= h
        if peak_x_sub > w // 2:
            peak_x_sub -= w
        
        # 计算置信度
        max_val = np.max(correlation)
        mean_val = np.mean(correlation)
        confidence = (max_val - mean_val) / (max_val + 1e-10)
        
        return peak_x_sub, peak_y_sub, confidence
    
    def extract_overlap_roi(self, img1, img2, direction):
        """
        智能提取重叠区域
        
        策略：
        - 根据ImageJ日志数据估计实际重叠度
        - 动态调整ROI尺寸
        - 确保有足够的特征用于匹配
        """
        h1, w1 = img1.shape
        
        if direction == 'horizontal':
            # 水平重叠：使用右侧和左侧区域
            estimated_overlap = (2448 - 2181) / 2448  # 基于ImageJ数据
            overlap_width = max(int(w1 * estimated_overlap), 200)
            roi1 = img1[:, -overlap_width:]  # 左图右侧
            roi2 = img2[:, :overlap_width]   # 右图左侧
        else:  # vertical
            # 垂直重叠：使用下侧和上侧区域
            estimated_overlap = (2048 - 1830) / 2048
            overlap_height = max(int(h1 * estimated_overlap), 200)
            roi1 = img1[-overlap_height:, :]  # 上图下侧
            roi2 = img2[:overlap_height, :]   # 下图上侧
        
        return roi1, roi2
    
    def calculate_single_pair_offset(self, pair_info):
        """计算单个图像对的偏移量"""
        (r1, c1), (r2, c2), direction = pair_info
        
        if (r1, c1) not in self.gray_images or (r2, c2) not in self.gray_images:
            return None
        
        img1 = self.gray_images[(r1, c1)]
        img2 = self.gray_images[(r2, c2)]
        
        try:
            # 提取重叠区域
            roi1, roi2 = self.extract_overlap_roi(img1, img2, direction)
            
            # 相位相关计算
            dx, dy, confidence = self.calculate_phase_correlation(roi1, roi2)
            
            # 调整偏移量到全图坐标系
            if direction == 'horizontal':
                actual_dx = img1.shape[1] - roi1.shape[1] + dx
                actual_dy = dy
            else:  # vertical
                actual_dx = dx
                actual_dy = img1.shape[0] - roi1.shape[0] + dy
            
            return {
                'pair': ((r1, c1), (r2, c2)),
                'offset': (actual_dx, actual_dy, confidence),
                'direction': direction
            }
        except Exception as e:
            return None
    
    def calculate_pairwise_offsets_parallel(self):
        """
        第二阶段：并行计算相邻图像偏移
        
        技术要点：
        - 水平和垂直方向分别处理
        - 多线程并行计算提升效率
        - 实时显示进度和统计信息
        """
        print(f"\n🔍 第二阶段：并行计算相位相关偏移")
        print(f"   🧵 使用 {self.num_threads} 个线程并行处理")
        
        start_time = time.time()
        
        # 准备所有图像对
        tasks = []
        # 水平相邻对
        for r in range(self.rows):
            for c in range(self.cols - 1):
                tasks.append(((r, c), (r, c + 1), 'horizontal'))
        # 垂直相邻对
        for r in range(self.rows - 1):
            for c in range(self.cols):
                tasks.append(((r, c), (r + 1, c), 'vertical'))
        
        print(f"   📊 总计 {len(tasks)} 个图像对")
        
        # 并行处理
        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            future_to_task = {executor.submit(self.calculate_single_pair_offset, task): task for task in tasks}
            
            valid_count = 0
            with tqdm(total=len(tasks), desc="🔄 相位相关", unit="对") as pbar:
                for future in as_completed(future_to_task):
                    result = future.result()
                    if result and result['offset'] is not None:
                        self.pairwise_offsets[result['pair']] = result['offset']
                        valid_count += 1
                    
                    pbar.set_postfix({'有效': valid_count, '总数': len(tasks)})
                    pbar.update(1)
        
        calc_time = time.time() - start_time
        print(f"   ✅ 计算完成：{valid_count}/{len(tasks)} 有效匹配，耗时 {calc_time:.2f}s")
        print(f"   ⚡ 平均每对耗时：{calc_time/len(tasks):.3f}s")

    # ========================= 第三阶段：全局优化 =========================
    
    def global_optimization(self):
        """
        第三阶段：全局优化消除累积误差
        
        核心思想：
        1. 固定(0,0)图像为坐标原点
        2. 将所有偏移约束转化为优化问题
        3. 使用最小二乘法求解最优位置
        4. 以置信度作为权重
        
        优势：
        - 消除逐步累积的误差
        - 全局一致性保证
        - 数值稳定性好
        """
        print(f"\n🎯 第三阶段：全局优化")
        
        # 创建变量映射（排除固定的(0,0)点）
        tile_to_idx = {}
        idx_to_tile = {}
        idx = 0
        
        for r in range(self.rows):
            for c in range(self.cols):
                if (r, c) in self.color_images and (r, c) != (0, 0):
                    tile_to_idx[(r, c)] = idx
                    idx_to_tile[idx] = (r, c)
                    idx += 1
        
        num_tiles = len(tile_to_idx)
        print(f"   📊 优化变量：{num_tiles} 个图像位置（固定(0,0)为原点）")
        
        # 构建约束方程
        constraints = []
        weights = []
        
        for pair, (dx, dy, confidence) in self.pairwise_offsets.items():
            tile1, tile2 = pair
            
            if tile1 == (0, 0) and tile2 in tile_to_idx:
                # 原点到变量的约束
                idx2 = tile_to_idx[tile2]
                constraints.append(('origin_to_var', idx2, dx, dy))
                weights.append(confidence)
            elif tile2 == (0, 0) and tile1 in tile_to_idx:
                # 变量到原点的约束
                idx1 = tile_to_idx[tile1]
                constraints.append(('var_to_origin', idx1, dx, dy))
                weights.append(confidence)
            elif tile1 in tile_to_idx and tile2 in tile_to_idx:
                # 变量间约束
                idx1 = tile_to_idx[tile1]
                idx2 = tile_to_idx[tile2]
                constraints.append(('var_to_var', idx1, idx2, dx, dy))
                weights.append(confidence)
        
        # 初始估计（简单累加）
        initial_positions = {(0, 0): (0.0, 0.0)}
        
        # 逐行累加估计
        for c in range(1, self.cols):
            if ((0, c-1), (0, c)) in self.pairwise_offsets:
                prev_x, prev_y = initial_positions[(0, c-1)]
                dx, dy, _ = self.pairwise_offsets[((0, c-1), (0, c))]
                initial_positions[(0, c)] = (prev_x + dx, prev_y + dy)
        
        for r in range(1, self.rows):
            for c in range(self.cols):
                if ((r-1, c), (r, c)) in self.pairwise_offsets and (r-1, c) in initial_positions:
                    prev_x, prev_y = initial_positions[(r-1, c)]
                    dx, dy, _ = self.pairwise_offsets[((r-1, c), (r, c))]
                    initial_positions[(r, c)] = (prev_x + dx, prev_y + dy)
        
        # 构建优化参数向量
        x0 = np.zeros(2 * num_tiles)
        for tile, (x, y) in initial_positions.items():
            if tile in tile_to_idx:
                idx = tile_to_idx[tile]
                x0[2*idx] = x
                x0[2*idx + 1] = y
        
        # 残差函数
        def residuals(params):
            residual_list = []
            for i, constraint in enumerate(constraints):
                weight = weights[i]
                
                if constraint[0] == 'origin_to_var':
                    _, idx2, dx_obs, dy_obs = constraint
                    x2, y2 = params[2*idx2], params[2*idx2 + 1]
                    dx_pred, dy_pred = x2, y2
                elif constraint[0] == 'var_to_origin':
                    _, idx1, dx_obs, dy_obs = constraint
                    x1, y1 = params[2*idx1], params[2*idx1 + 1]
                    dx_pred, dy_pred = -x1, -y1
                elif constraint[0] == 'var_to_var':
                    _, idx1, idx2, dx_obs, dy_obs = constraint
                    x1, y1 = params[2*idx1], params[2*idx1 + 1]
                    x2, y2 = params[2*idx2], params[2*idx2 + 1]
                    dx_pred = x2 - x1
                    dy_pred = y2 - y1
                
                residual_list.extend([
                    weight * (dx_pred - dx_obs),
                    weight * (dy_pred - dy_obs)
                ])
            
            return np.array(residual_list)
        
        # 执行优化
        start_time = time.time()
        result = least_squares(residuals, x0, method='lm')
        opt_time = time.time() - start_time
        
        # 提取优化结果
        self.final_positions = {(0, 0): (0.0, 0.0)}
        
        if result.success:
            for idx in range(num_tiles):
                tile = idx_to_tile[idx]
                x_opt = result.x[2*idx]
                y_opt = result.x[2*idx + 1]
                self.final_positions[tile] = (x_opt, y_opt)
            print(f"   ✅ 优化收敛成功，耗时 {opt_time:.3f}s")
        else:
            print(f"   ⚠️  优化未收敛，使用初始估计")
            for tile, pos in initial_positions.items():
                if tile != (0, 0):
                    self.final_positions[tile] = pos

    # ========================= 第四阶段：图像融合 =========================
    
    def calculate_canvas_size(self):
        """计算拼接画布尺寸"""
        if not self.final_positions:
            raise ValueError("未计算图像位置")
        
        sample_img = next(iter(self.color_images.values()))
        img_height, img_width = sample_img.shape[:2]
        
        # 计算边界
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        for (r, c), (x, y) in self.final_positions.items():
            if (r, c) in self.color_images:
                corners = [(x, y), (x + img_width, y), (x, y + img_height), (x + img_width, y + img_height)]
                for corner_x, corner_y in corners:
                    min_x, max_x = min(min_x, corner_x), max(max_x, corner_x)
                    min_y, max_y = min(min_y, corner_y), max(max_y, corner_y)
        
        canvas_width = int(np.ceil(max_x - min_x))
        canvas_height = int(np.ceil(max_y - min_y))
        offset_x, offset_y = -min_x, -min_y
        
        return canvas_width, canvas_height, offset_x, offset_y
    
    def create_distance_weight_map(self, img_shape):
        """创建基于距离的权重图"""
        h, w = img_shape[:2]
        
        # 创建距离场
        y_indices, x_indices = np.ogrid[:h, :w]
        
        # 计算到边界的距离
        dist_to_top = y_indices
        dist_to_bottom = h - 1 - y_indices
        dist_to_left = x_indices
        dist_to_right = w - 1 - x_indices
        
        # 距离权重图
        dist_to_edge = np.minimum(
            np.minimum(dist_to_top, dist_to_bottom),
            np.minimum(dist_to_left, dist_to_right)
        )
        
        # 设置羽化参数
        feather_pixels = min(self.max_feather_pixels, min(h, w) // 10)
        feather_pixels = max(feather_pixels, 5)
        
        if feather_pixels > 5:
            sigma = feather_pixels / 3.0
            weight_map = np.exp(-((feather_pixels - dist_to_edge) ** 2) / (2 * sigma ** 2))
            weight_map = np.clip(weight_map, 0.1, 1.0)
            
            inner_mask = dist_to_edge >= feather_pixels
            weight_map[inner_mask] = 1.0
        else:
            weight_map = np.ones((h, w), dtype=np.float32)
        
        return weight_map.astype(np.float32)
    
    def blend_images_intelligent(self, output_path):
        """
        第四阶段：智能图像融合 - 优先GPU加速
        
        技术特点：
        1. 优先使用GPU分块融合
        2. GPU不可用时回退到CPU高性能模式
        3. 亚像素精度变换
        4. Linear blending权重融合
        """
        print(f"\n🎨 第四阶段：智能图像融合")
        
        canvas_width, canvas_height, offset_x, offset_y = self.calculate_canvas_size()
        memory_gb = (canvas_width * canvas_height * 3 * 8) / (1024**3)
        
        print(f"   📏 画布尺寸：{canvas_width} × {canvas_height}")
        print(f"   💾 内存需求：{memory_gb:.2f} GB")
        
        # 优先尝试GPU融合
        if PYTORCH_AVAILABLE and torch.cuda.is_available() and self.use_gpu:
            print(f"   🎮 使用GPU加速融合模式")
            return self._blend_gpu_accelerated(canvas_width, canvas_height, offset_x, offset_y, output_path)
        else:
            print(f"   💾 GPU不可用，使用CPU内存高效模式")
            return self._blend_memory_efficient(canvas_width, canvas_height, offset_x, offset_y, output_path)

    def _blend_gpu_accelerated(self, canvas_width, canvas_height, offset_x, offset_y, output_path):
        """GPU加速图像融合"""
        start_time = time.time()
        
        # 清理GPU缓存
        torch.cuda.empty_cache()
        
        # 计算内存需求
        required_memory_gb = (canvas_width * canvas_height * 4 * 4) / (1024**3)  # float32 * 4 channels
        chunk_memory_gb = (self.gpu_chunk_size * self.gpu_chunk_size * 4 * 4) / (1024**3)
        
        print(f"   🔧 GPU配置:")
        print(f"      显存限制: {self.gpu_memory_limit_gb:.1f} GB")
        print(f"      预估需求: {required_memory_gb:.1f} GB")
        print(f"      分块大小: {self.gpu_chunk_size} x {self.gpu_chunk_size}")
        print(f"      分块内存: {chunk_memory_gb:.1f} GB")
        
        # 强制使用分块模式以适应4GB显存
        return self._blend_gpu_chunked(canvas_width, canvas_height, offset_x, offset_y, output_path)

    def _blend_gpu_chunked(self, canvas_width, canvas_height, offset_x, offset_y, output_path):
        """GPU分块融合 - 专为4GB显存优化"""
        print(f"   🧩 GPU分块融合模式")
        
        start_time = time.time()
        device = torch.device('cuda')
        chunk_size = self.gpu_chunk_size
        
        try:
            # 创建最终结果画布（在CPU上）
            result = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)
            
            # 计算分块数量
            y_chunks = (canvas_height + chunk_size - 1) // chunk_size
            x_chunks = (canvas_width + chunk_size - 1) // chunk_size
            total_chunks = y_chunks * x_chunks
            
            print(f"      分块数量: {y_chunks} x {x_chunks} = {total_chunks}")
            
            # 预处理图像位置信息
            image_positions = {}
            for (r, c), (x, y) in self.final_positions.items():
                if (r, c) in self.color_images:
                    img = self.color_images[(r, c)]
                    h, w = img.shape[:2]
                    x_adj = int(x + offset_x)
                    y_adj = int(y + offset_y)
                    image_positions[(r, c)] = {
                        'img': img,
                        'x': x_adj, 'y': y_adj, 
                        'h': h, 'w': w,
                        'weight_map': self.create_distance_weight_map(img.shape)
                    }
            
            # 使用进度条显示分块处理进度
            with tqdm(total=total_chunks, desc="🧩 GPU分块融合", ncols=80) as pbar:
                chunk_count = 0
                for y_start in range(0, canvas_height, chunk_size):
                    y_end = min(y_start + chunk_size, canvas_height)
                    
                    for x_start in range(0, canvas_width, chunk_size):
                        x_end = min(x_start + chunk_size, canvas_width)
                        chunk_count += 1
                        
                        chunk_h = y_end - y_start
                        chunk_w = x_end - x_start
                        
                        # 清理GPU缓存
                        torch.cuda.empty_cache()
                        
                        # 在GPU上创建块画布
                        chunk_canvas = torch.zeros((chunk_h, chunk_w, 3), dtype=torch.float32, device=device)
                        chunk_weight = torch.zeros((chunk_h, chunk_w), dtype=torch.float32, device=device)
                        
                        # 处理与当前块重叠的图像
                        images_in_chunk = 0
                        for (r, c), img_info in image_positions.items():
                            img = img_info['img']
                            img_x, img_y = img_info['x'], img_info['y']
                            img_h, img_w = img_info['h'], img_info['w']
                            weight_map = img_info['weight_map']
                            
                            # 检查图像是否与当前块重叠
                            if (img_x + img_w <= x_start or img_x >= x_end or 
                                img_y + img_h <= y_start or img_y >= y_end):
                                continue
                            
                            images_in_chunk += 1
                            
                            # 计算重叠区域
                            overlap_x1 = max(img_x, x_start)
                            overlap_y1 = max(img_y, y_start)
                            overlap_x2 = min(img_x + img_w, x_end)
                            overlap_y2 = min(img_y + img_h, y_end)
                            
                            if overlap_x2 <= overlap_x1 or overlap_y2 <= overlap_y1:
                                continue
                            
                            # 在图像中的坐标
                            img_x1 = overlap_x1 - img_x
                            img_y1 = overlap_y1 - img_y
                            img_x2 = overlap_x2 - img_x
                            img_y2 = overlap_y2 - img_y
                            
                            # 在块中的坐标
                            chunk_x1 = overlap_x1 - x_start
                            chunk_y1 = overlap_y1 - y_start
                            chunk_x2 = overlap_x2 - x_start
                            chunk_y2 = overlap_y2 - y_start
                            
                            # 提取图像和权重区域
                            img_roi = img[img_y1:img_y2, img_x1:img_x2].astype(np.float32)
                            weight_roi = weight_map[img_y1:img_y2, img_x1:img_x2]
                            
                            # 转换到GPU
                            img_tensor = torch.from_numpy(img_roi).to(device)
                            weight_tensor = torch.from_numpy(weight_roi).to(device)
                            
                            # 融合到块画布
                            for ch in range(3):
                                chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2, ch] += img_tensor[:, :, ch] * weight_tensor
                            
                            chunk_weight[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += weight_tensor
                        
                        # 归一化块
                        chunk_weight = torch.clamp(chunk_weight, min=1e-10)
                        
                        chunk_result = torch.zeros_like(chunk_canvas)
                        for ch in range(3):
                            chunk_result[:, :, ch] = chunk_canvas[:, :, ch] / chunk_weight
                        
                        chunk_result = torch.clamp(chunk_result, 0, 255)
                        chunk_final = chunk_result.byte().cpu().numpy()
                        
                        # 保存到最终结果
                        result[y_start:y_end, x_start:x_end] = chunk_final
                        
                        # 更新进度条
                        pbar.set_postfix({
                            '块': f'{chunk_count}/{total_chunks}', 
                            '图像': images_in_chunk,
                            'GPU内存': f'{torch.cuda.memory_allocated()/1024**2:.0f}MB'
                        })
                        pbar.update(1)
                        
                        # 清理GPU内存
                        del chunk_canvas, chunk_weight, chunk_result, chunk_final, img_tensor, weight_tensor
                        torch.cuda.empty_cache()
            
            # 保存结果
            cv2.imwrite(output_path, result)
            
            blend_time = time.time() - start_time
            print(f"   ✅ GPU分块融合完成，耗时 {blend_time:.2f}s")
            return result
            
        except Exception as e:
            print(f"   ❌ GPU融合失败: {e}")
            print(f"   🔄 回退到CPU内存高效模式")
            torch.cuda.empty_cache()
            return self._blend_memory_efficient(canvas_width, canvas_height, offset_x, offset_y, output_path)
    

    
    def _blend_memory_efficient(self, canvas_width, canvas_height, offset_x, offset_y, output_path):
        """内存高效融合"""
        print(f"   💾 使用内存高效模式")
        start_time = time.time()
        
        canvas = np.zeros((canvas_height, canvas_width, 3), dtype=np.float32)
        weight_canvas = np.zeros((canvas_height, canvas_width), dtype=np.float32)
        
        with tqdm(total=len(self.final_positions), desc="🎨 融合图像", unit="张") as pbar:
            for (r, c), (x, y) in self.final_positions.items():
                if (r, c) not in self.color_images:
                    pbar.update(1)
                    continue
                
                img = self.color_images[(r, c)]
                h, w = img.shape[:2]
                
                # 计算放置位置
                x_adj = (x + offset_x)
                y_adj = (y + offset_y)
                weight_map = self.create_distance_weight_map(img.shape)
                
                # 计算覆盖区域
                x_start = int(np.floor(x_adj))
                y_start = int(np.floor(y_adj))
                x_end = min(x_start + w, canvas_width)
                y_end = min(y_start + h, canvas_height)
                
                if x_start < canvas_width and y_start < canvas_height and x_end > 0 and y_end > 0:
                    # 计算有效区域
                    img_x_start = max(0, -x_start)
                    img_y_start = max(0, -y_start)
                    img_x_end = img_x_start + (x_end - max(0, x_start))
                    img_y_end = img_y_start + (y_end - max(0, y_start))
                    
                    canvas_x_start = max(0, x_start)
                    canvas_y_start = max(0, y_start)
                    
                    if img_x_end > img_x_start and img_y_end > img_y_start:
                        img_region = img[img_y_start:img_y_end, img_x_start:img_x_end]
                        weight_region = weight_map[img_y_start:img_y_end, img_x_start:img_x_end]
                        
                        # 累积到画布
                        for c_idx in range(3):
                            canvas[canvas_y_start:canvas_y_start + img_region.shape[0], 
                                  canvas_x_start:canvas_x_start + img_region.shape[1], c_idx] += (
                                img_region[:, :, c_idx].astype(np.float32) * weight_region
                            )
                        
                        weight_canvas[canvas_y_start:canvas_y_start + img_region.shape[0], 
                                     canvas_x_start:canvas_x_start + img_region.shape[1]] += weight_region
                
                pbar.update(1)
        
        # 归一化
        mask = weight_canvas > 0
        result = np.zeros_like(canvas, dtype=np.uint8)
        for c_idx in range(3):
            result[:, :, c_idx][mask] = (canvas[:, :, c_idx][mask] / weight_canvas[mask]).astype(np.uint8)
        
        cv2.imwrite(output_path, result)
        
        blend_time = time.time() - start_time
        print(f"   ✅ 内存高效融合完成，耗时 {blend_time:.2f}s")
        return result

    # ========================= 主要接口和工具函数 =========================
    
    def save_configuration(self, filename="TileConfiguration_opencv_subpixel.txt"):
        """保存配置文件"""
        with open(filename, 'w') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n\n")
            f.write("# Define the image coordinates (subpixel precision)\n")
            
            for (r, c), (x, y) in self.final_positions.items():
                if (r, c) in self.color_images:
                    f.write(f"r{r:03d}_c{c:03d}.jpg; ; ({x:.6f}, {y:.6f})\n")
        
        print(f"📄 配置文件已保存: {filename}")
    
    
    
    def run_complete_stitching(self, output_path=None):
        """
        执行完整的图像拼接流程
        
        返回: (最终位置字典, 拼接结果图像)
        """
        if output_path is None:
            output_path = f"{self.image_dir}_stitched_advanced.jpg"
        
        total_start = time.time()
        
        print("🚀 " + "="*70)
        print("🚀 高性能图像拼接 - 亚像素精度配准")
        print("🚀 " + "="*70)
        
        try:
            # 第一阶段：加载图像
            step1_start = time.time()
            self.load_images_parallel()
            step1_time = time.time() - step1_start
            
            # 第二阶段：相位相关
            step2_start = time.time()
            self.calculate_pairwise_offsets_parallel()
            step2_time = time.time() - step2_start
            
            # 第三阶段：全局优化
            step3_start = time.time()
            self.global_optimization()
            step3_time = time.time() - step3_start
            
            # 第四阶段：图像融合
            step4_start = time.time()
            result_image = self.blend_images_intelligent(output_path)
            step4_time = time.time() - step4_start
            
            # 保存配置并比较
            self.save_configuration()
            
            # 总结
            total_time = time.time() - total_start
            print("\n🏁 " + "="*70)
            print("🏁 拼接完成!")
            print("🏁 " + "="*70)
            print(f"⏱️  阶段1 (图像加载):     {step1_time:8.2f}s")
            print(f"⏱️  阶段2 (相位相关):     {step2_time:8.2f}s") 
            print(f"⏱️  阶段3 (全局优化):     {step3_time:8.2f}s")
            print(f"⏱️  阶段4 (图像融合):     {step4_time:8.2f}s")
            print(f"⏱️  总耗时:              {total_time:8.2f}s")
            print("🏁 " + "="*70)
            print(f"📸 结果保存至: {output_path}")
            
            return self.final_positions, result_image
            
        except Exception as e:
            print(f"❌ 拼接失败: {str(e)}")
            raise

def main():
    """主函数 - 直接指定融合模式"""
    
    # ================== 在这里设置模式 ==================
    MODE = "GPU"  # 改为 "GPU" 或 "CPU"
    # ==================================================
    
    print("🚀 高性能图像拼接系统")
    print("=" * 50)
    
    # 检测GPU状态
    gpu_available = PYTORCH_AVAILABLE and torch.cuda.is_available()
    if gpu_available:
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"🎮 检测到GPU: {gpu_name}")
        print(f"💾 显存容量: {gpu_memory:.1f} GB")
    else:
        print("❌ 未检测到可用GPU")
    
    # 解析模式设置
    choice = MODE.upper()
    use_gpu = False
    
    if choice == 'GPU':
        if not gpu_available:
            print("❌ GPU不可用，自动切换到CPU内存高效模式")
            use_gpu = False
        else:
            print("🎮 模式: GPU加速融合")
            use_gpu = True
    else:
        print("💾 模式: CPU内存高效融合")
        use_gpu = False
    
    # 基础参数配置
    config = {
        'image_dir': "test02",  # 图像目录
        'rows': 6,              # 网格行数
        'cols': 16,             # 网格列数
        'overlap_ratio': 0.1,   # 预估重叠度
        'num_threads': 20       # 线程数
    }
    
    # 创建拼接器
    stitcher = GridStitcher(**config)
    
    # 根据用户选择设置融合模式
    if use_gpu:
        stitcher.use_gpu = True
        stitcher.force_gpu_chunked = True
        print(f"🔧 配置GPU参数: 显存限制{stitcher.gpu_memory_limit_gb}GB, 分块{stitcher.gpu_chunk_size}x{stitcher.gpu_chunk_size}")
    else:
        stitcher.use_gpu = False
        print("🔧 配置CPU内存高效模式")
        # 强制使用CPU内存高效模式
        original_blend = stitcher.blend_images_intelligent
        def force_cpu_memory_blend(output_path):
            print(f"\n🎨 第四阶段：CPU内存高效融合")
            canvas_width, canvas_height, offset_x, offset_y = stitcher.calculate_canvas_size()
            memory_gb = (canvas_width * canvas_height * 3 * 8) / (1024**3)
            print(f"   📏 画布尺寸：{canvas_width} × {canvas_height}")
            print(f"   💾 内存需求：{memory_gb:.2f} GB")
            print(f"   💾 使用CPU内存高效模式")
            return stitcher._blend_memory_efficient(canvas_width, canvas_height, offset_x, offset_y, output_path)
        stitcher.blend_images_intelligent = force_cpu_memory_blend
    
    print(f"\n🎯 开始处理 {config['rows']}×{config['cols']} = {config['rows']*config['cols']} 张图像")
    
    try:
        # 执行拼接
        positions, result_image = stitcher.run_complete_stitching()
        
        print(f"\n✅ 拼接成功完成!")
        print(f"📊 最终图像尺寸: {result_image.shape}")
        print(f"🎯 处理了 {len(positions)} 张图像")
        
        # 显示文件信息
        output_path = f"{config['image_dir']}_stitched_advanced.jpg"
        if os.path.exists(output_path):
            file_size_mb = os.path.getsize(output_path) / (1024 * 1024)
            print(f"💾 输出文件大小: {file_size_mb:.1f} MB")
            
    except Exception as e:
        print(f"\n❌ 拼接失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
