import cv2
import numpy as np
import os
import re
import time
import multiprocessing
from scipy.optimize import least_squares
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import psutil


class StitchingConfig:
    """拼接配置类 - 统一管理所有参数"""
    
    def __init__(self):
        # 基本参数
        self.image_dir = None
        self.rows = 3
        self.cols = 3
        self.overlap_ratio = 0.1
        self.num_threads = multiprocessing.cpu_count()
        
        # 输入格式
        self.tile_config_path = None
        self.image_naming_format = "r_c"  # "r_c" 或 "s_sequential"
        
        # 融合参数
        self.max_feather_pixels = 50
        self.memory_limit_gb = 5  # 设置为1GB，确保触发分块模式
        self.chunk_size = 4096  # OpenCV处理块大小
        
        # 优化参数
        self.confidence_threshold = 0.5
        self.long_distance_weight = 0.3
        self.smoothness_weight = 0.1
    
    @classmethod
    def from_directory(cls, image_dir, rows=None, cols=None, overlap_ratio=0.1):
        """从目录创建配置"""
        config = cls()
        config.image_dir = image_dir
        if rows: config.rows = rows
        if cols: config.cols = cols
        config.overlap_ratio = overlap_ratio
        return config
    
    @classmethod
    def from_tile_config(cls, image_dir, tile_config_path):
        """从TileConfiguration.txt文件创建配置"""
        config = cls()
        config.image_dir = image_dir
        config.tile_config_path = tile_config_path
        return config


class ImageLoader:
    """图像加载器 - 处理图像加载和格式解析"""
    
    def __init__(self, config):
        self.config = config
        self.color_images = {}
        self.gray_images = {}
        
    def load_all_images(self):
        """加载所有图像"""
        print(f"\n📋 第一阶段：并行加载图像")
        print(f"   🧵 使用 {self.config.num_threads} 个线程并行加载")
        
        # 构建文件名映射
        grid_to_filename = self._build_filename_mapping()
        
        def load_single_image(r, c):
            """加载单张图像"""
            filename = self._get_filename(r, c, grid_to_filename)
            if not filename:
                return (r, c), None, None, False
                
            filepath = os.path.join(self.config.image_dir, filename)
            if os.path.exists(filepath):
                color_img = cv2.imread(filepath, cv2.IMREAD_COLOR)
                if color_img is not None:
                    gray_img = cv2.cvtColor(color_img, cv2.COLOR_BGR2GRAY)
                    return (r, c), color_img, gray_img, True
            return (r, c), None, None, False
        
        # 并行加载
        start_time = time.time()
        tasks = [(r, c) for r in range(self.config.rows) for c in range(self.config.cols)]
        
        with ThreadPoolExecutor(max_workers=self.config.num_threads) as executor:
            future_to_pos = {executor.submit(load_single_image, r, c): (r, c) for r, c in tasks}
            
            with tqdm(total=len(tasks), desc="🖼️  加载图像", unit="张") as pbar:
                for future in as_completed(future_to_pos):
                    (r, c), color_img, gray_img, success = future.result()
                    if success:
                        self.color_images[(r, c)] = color_img
                        self.gray_images[(r, c)] = gray_img
                    pbar.update(1)
        
        load_time = time.time() - start_time
        print(f"   ✅ 加载完成：{len(self.color_images)} 张图像，耗时 {load_time:.2f}s")
        
        if not self.color_images:
            raise ValueError("❌ 未找到任何图像文件！")
    
    def _build_filename_mapping(self):
        """构建文件名映射"""
        if not self.config.tile_config_path:
            return {}
            
        # 解析TileConfiguration.txt
        grid_info = self._parse_tile_configuration()
        self.config.rows = grid_info['rows']
        self.config.cols = grid_info['cols']
        self.config.overlap_ratio = grid_info['overlap_ratio']
        self.config.image_naming_format = grid_info['naming_format']
        
        return grid_info.get('grid_to_filename', {})
    
    def _parse_tile_configuration(self):
        """解析TileConfiguration.txt文件"""
        config_path = self.config.tile_config_path
        print(f"   📋 解析配置文件: {config_path}")
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        positions = {}
        with open(config_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('#') or line.startswith('dim') or not line:
                    continue
                
                match = re.match(r'(\S+)\s*;\s*;\s*\(([-\d.]+),\s*([-\d.]+)\)', line)
                if match:
                    filename = match.group(1)
                    x = float(match.group(2))
                    y = float(match.group(3))
                    positions[filename] = (x, y)
        
        return self._analyze_grid_structure(positions)
    
    def _analyze_grid_structure(self, positions):
        """分析网格结构"""
        coords = list(positions.values())
        x_coords = sorted(list(set([coord[0] for coord in coords])))
        y_coords = sorted(list(set([coord[1] for coord in coords])))
        
        rows, cols = len(y_coords), len(x_coords)
        
        # 计算重叠度
        if len(x_coords) > 1:
            x_spacing = x_coords[1] - x_coords[0]
            overlap_ratio = max(0, (2448 - x_spacing) / 2448)  # 假设图像宽度2448
        else:
            overlap_ratio = 0.1
        
        # 检测命名格式
        sample_filename = list(positions.keys())[0]
        naming_format = "s_sequential" if sample_filename.startswith('s_') else "r_c"
        
        # 构建网格到文件名的映射
        grid_to_filename = {}
        if naming_format == "s_sequential":
            sorted_files = sorted(positions.keys(), key=lambda x: int(x.split('_')[1].split('.')[0]))
            for idx, filename in enumerate(sorted_files):
                row = idx // cols
                col = (idx % cols) if row % 2 == 0 else (cols - 1 - (idx % cols))
                grid_to_filename[(row, col)] = filename
        
        print(f"   ✅ 检测到 {rows}×{cols} 网格，重叠度 {overlap_ratio*100:.1f}%")
        
        return {
            'rows': rows,
            'cols': cols,
            'overlap_ratio': overlap_ratio,
            'naming_format': naming_format,
            'grid_to_filename': grid_to_filename
        }
    
    def _get_filename(self, r, c, grid_to_filename):
        """获取指定位置的文件名"""
        if grid_to_filename and (r, c) in grid_to_filename:
            return grid_to_filename[(r, c)]
        else:
            return f"r{r:03d}_c{c:03d}.jpg"


class PhaseCorrelationMatcher:
    """相位相关匹配器 - 处理图像配准"""
    
    def __init__(self, config):
        self.config = config
        self.pairwise_offsets = {}
    
    def calculate_all_offsets(self, gray_images):
        """计算所有相邻图像的偏移量"""
        print(f"\n🔍 第二阶段：并行计算相位相关偏移")
        
        # 准备所有图像对
        tasks = []
        for r in range(self.config.rows):
            for c in range(self.config.cols - 1):
                tasks.append(((r, c), (r, c + 1), 'horizontal'))
        for r in range(self.config.rows - 1):
            for c in range(self.config.cols):
                tasks.append(((r, c), (r + 1, c), 'vertical'))
        
        # 并行处理
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=self.config.num_threads) as executor:
            future_to_task = {
                executor.submit(self._calculate_single_offset, task, gray_images): task 
                for task in tasks
            }
            
            valid_count = 0
            with tqdm(total=len(tasks), desc="🔄 相位相关", unit="对") as pbar:
                for future in as_completed(future_to_task):
                    result = future.result()
                    if result:
                        self.pairwise_offsets[result['pair']] = result['offset']
                        valid_count += 1
                    pbar.update(1)
        
        calc_time = time.time() - start_time
        print(f"   ✅ 计算完成：{valid_count}/{len(tasks)} 有效匹配，耗时 {calc_time:.2f}s")
    
    def _calculate_single_offset(self, pair_info, gray_images):
        """计算单个图像对的偏移量"""
        (r1, c1), (r2, c2), direction = pair_info
        
        if (r1, c1) not in gray_images or (r2, c2) not in gray_images:
            return None
        
        img1, img2 = gray_images[(r1, c1)], gray_images[(r2, c2)]
        
        try:
            roi1, roi2 = self._extract_overlap_roi(img1, img2, direction)
            dx, dy, confidence = self._phase_correlation(roi1, roi2)
            
            # 调整到全图坐标系
            if direction == 'horizontal':
                actual_dx = img1.shape[1] - roi1.shape[1] + dx
                actual_dy = dy
            else:
                actual_dx = dx
                actual_dy = img1.shape[0] - roi1.shape[0] + dy
            
            return {
                'pair': ((r1, c1), (r2, c2)),
                'offset': (actual_dx, actual_dy, confidence),
                'direction': direction
            }
        except Exception:
            return None
    
    def _extract_overlap_roi(self, img1, img2, direction):
        """提取重叠区域"""
        h1, w1 = img1.shape
        
        if direction == 'horizontal':
            overlap_width = max(int(w1 * self.config.overlap_ratio), 200)
            roi1 = img1[:, -overlap_width:]
            roi2 = img2[:, :overlap_width]
        else:
            overlap_height = max(int(h1 * self.config.overlap_ratio), 200)
            roi1 = img1[-overlap_height:, :]
            roi2 = img2[:overlap_height, :]
        
        return roi1, roi2
    
    def _phase_correlation(self, img1, img2):
        """相位相关核心算法"""
        def apply_hanning_window(img):
            h, w = img.shape
            hann_h = np.hanning(h).reshape(-1, 1)
            hann_w = np.hanning(w).reshape(1, -1)
            return img.astype(np.float32) * (hann_h * hann_w)
        
        # 确保相同尺寸
        if img1.shape != img2.shape:
            h = min(img1.shape[0], img2.shape[0])
            w = min(img1.shape[1], img2.shape[1])
            img1, img2 = img1[:h, :w], img2[:h, :w]
        
        # 应用窗函数
        img1_windowed = apply_hanning_window(img1)
        img2_windowed = apply_hanning_window(img2)
        
        # FFT计算
        f1 = np.fft.fft2(img1_windowed)
        f2 = np.fft.fft2(img2_windowed)
        
        # 相位相关
        cross_power = f1 * np.conj(f2)
        cross_power_abs = np.abs(cross_power) + 1e-15
        cross_power_norm = cross_power / cross_power_abs
        
        correlation = np.real(np.fft.ifft2(cross_power_norm))
        correlation = np.fft.fftshift(correlation)
        
        # 亚像素峰值检测
        h, w = correlation.shape
        peak_y, peak_x = np.unravel_index(np.argmax(correlation), correlation.shape)
        
        # 抛物线拟合亚像素精化
        if 0 < peak_x < w-1 and 0 < peak_y < h-1:
            # X方向
            c1, c2, c3 = correlation[peak_y, peak_x-1:peak_x+2]
            dx = 0.5 * (c1 - c3) / (c1 + c3 - 2*c2) if abs(c1 + c3 - 2*c2) > 1e-10 else 0
            
            # Y方向
            c1, c2, c3 = correlation[peak_y-1:peak_y+2, peak_x]
            dy = 0.5 * (c1 - c3) / (c1 + c3 - 2*c2) if abs(c1 + c3 - 2*c2) > 1e-10 else 0
        else:
            dx = dy = 0
        
        # 转换到图像坐标系
        center_x, center_y = w // 2, h // 2
        offset_x = peak_x + dx - center_x
        offset_y = peak_y + dy - center_y
        
        # 计算置信度
        max_val = correlation[peak_y, peak_x]
        mask = np.ones_like(correlation, dtype=bool)
        mask[max(0, peak_y-2):peak_y+3, max(0, peak_x-2):peak_x+3] = False
        bg_mean = correlation[mask].mean()
        bg_std = correlation[mask].std()
        confidence = min(1.0, (max_val - bg_mean) / (bg_std + 1e-10) / 20.0)
        
        return offset_x, offset_y, confidence


class GlobalOptimizer:
    """全局优化器 - 消除累积误差"""
    
    def __init__(self, config):
        self.config = config
        self.final_positions = {}
    
    def optimize_positions(self, pairwise_offsets, color_images):
        """全局优化所有图像位置"""
        print(f"\n🎯 第三阶段：全局优化")
        
        # 构建变量映射
        tile_to_idx = {}
        idx_to_tile = {}
        idx = 0
        
        for r in range(self.config.rows):
            for c in range(self.config.cols):
                if (r, c) in color_images and (r, c) != (0, 0):
                    tile_to_idx[(r, c)] = idx
                    idx_to_tile[idx] = (r, c)
                    idx += 1
        
        print(f"   📊 优化变量：{len(tile_to_idx)} 个图像位置")
        
        # 构建约束和权重
        constraints, weights = self._build_constraints(pairwise_offsets, tile_to_idx)
        
        # 初始位置估计
        x0 = self._estimate_initial_positions(pairwise_offsets, tile_to_idx, color_images)
        
        # 执行优化
        start_time = time.time()
        result = least_squares(
            lambda params: self._residuals(params, constraints, weights, tile_to_idx),
            x0, method='trf', loss='soft_l1'
        )
        
        # 提取结果
        self.final_positions = {(0, 0): (0.0, 0.0)}
        if result.success:
            for idx in range(len(tile_to_idx)):
                tile = idx_to_tile[idx]
                self.final_positions[tile] = (result.x[2*idx], result.x[2*idx + 1])
        
        opt_time = time.time() - start_time
        print(f"   ✅ 优化完成，耗时 {opt_time:.3f}s")
    
    def _build_constraints(self, pairwise_offsets, tile_to_idx):
        """构建优化约束"""
        constraints = []
        weights = []
        
        # 相邻图像约束
        for pair, (dx, dy, confidence) in pairwise_offsets.items():
            tile1, tile2 = pair
            
            if tile1 == (0, 0) and tile2 in tile_to_idx:
                constraints.append(('origin_to_var', tile_to_idx[tile2], dx, dy))
                weights.append(confidence)
            elif tile2 == (0, 0) and tile1 in tile_to_idx:
                constraints.append(('var_to_origin', tile_to_idx[tile1], dx, dy))
                weights.append(confidence)
            elif tile1 in tile_to_idx and tile2 in tile_to_idx:
                constraints.append(('var_to_var', tile_to_idx[tile1], tile_to_idx[tile2], dx, dy))
                weights.append(confidence)
        
        # 添加长距离约束
        long_distance_count = self._add_long_distance_constraints(
            constraints, weights, pairwise_offsets, tile_to_idx
        )
        
        print(f"   ➕ 添加了 {long_distance_count} 个长距离约束")
        return constraints, weights
    
    def _add_long_distance_constraints(self, constraints, weights, pairwise_offsets, tile_to_idx):
        """添加长距离约束减少累积误差"""
        count = 0
        
        # 水平长距离连接
        for r in range(self.config.rows):
            for c1 in range(self.config.cols):
                for step in [2, 3]:
                    c2 = c1 + step
                    if (c2 < self.config.cols and 
                        (r, c1) in tile_to_idx and (r, c2) in tile_to_idx):
                        
                        # 计算累积偏移
                        est_dx, est_dy = 0, 0
                        valid = True
                        
                        for i in range(step):
                            if ((r, c1+i), (r, c1+i+1)) in pairwise_offsets:
                                dx, dy, _ = pairwise_offsets[((r, c1+i), (r, c1+i+1))]
                                est_dx += dx
                                est_dy += dy
                            else:
                                valid = False
                                break
                        
                        if valid:
                            constraints.append((
                                'var_to_var', 
                                tile_to_idx[(r, c1)], 
                                tile_to_idx[(r, c2)], 
                                est_dx, est_dy
                            ))
                            weights.append(self.config.long_distance_weight)
                            count += 1
        
        # 垂直长距离连接
        for c in range(self.config.cols):
            for r1 in range(self.config.rows):
                for step in [2, 3]:
                    r2 = r1 + step
                    if (r2 < self.config.rows and 
                        (r1, c) in tile_to_idx and (r2, c) in tile_to_idx):
                        
                        est_dx, est_dy = 0, 0
                        valid = True
                        
                        for i in range(step):
                            if ((r1+i, c), (r1+i+1, c)) in pairwise_offsets:
                                dx, dy, _ = pairwise_offsets[((r1+i, c), (r1+i+1, c))]
                                est_dx += dx
                                est_dy += dy
                            else:
                                valid = False
                                break
                        
                        if valid:
                            constraints.append((
                                'var_to_var', 
                                tile_to_idx[(r1, c)], 
                                tile_to_idx[(r2, c)], 
                                est_dx, est_dy
                            ))
                            weights.append(self.config.long_distance_weight)
                            count += 1
        
        return count
    
    def _estimate_initial_positions(self, pairwise_offsets, tile_to_idx, color_images):
        """估计初始位置"""
        initial_positions = {(0, 0): (0.0, 0.0)}
        
        # 从原点开始扩展
        for pair, (dx, dy, _) in pairwise_offsets.items():
            tile1, tile2 = pair
            if tile1 == (0, 0) and tile2 in color_images:
                initial_positions[tile2] = (dx, dy)
            elif tile2 == (0, 0) and tile1 in color_images:
                initial_positions[tile1] = (-dx, -dy)
        
        # 逐步扩展
        while True:
            new_positions = {}
            for pair, (dx, dy, _) in pairwise_offsets.items():
                tile1, tile2 = pair
                
                if tile1 in initial_positions and tile2 not in initial_positions:
                    x1, y1 = initial_positions[tile1]
                    new_positions[tile2] = (x1 + dx, y1 + dy)
                elif tile2 in initial_positions and tile1 not in initial_positions:
                    x2, y2 = initial_positions[tile2]
                    new_positions[tile1] = (x2 - dx, y2 - dy)
            
            if not new_positions:
                break
            initial_positions.update(new_positions)
        
        # 构建参数向量
        x0 = np.zeros(2 * len(tile_to_idx))
        for tile, (x, y) in initial_positions.items():
            if tile in tile_to_idx:
                idx = tile_to_idx[tile]
                x0[2*idx] = x
                x0[2*idx + 1] = y
        
        return x0
    
    def _residuals(self, params, constraints, weights, tile_to_idx):
        """计算残差"""
        residuals = []
        
        for i, constraint in enumerate(constraints):
            weight = weights[i]
            
            if constraint[0] == 'origin_to_var':
                _, idx2, dx_obs, dy_obs = constraint
                dx_pred, dy_pred = params[2*idx2], params[2*idx2 + 1]
            elif constraint[0] == 'var_to_origin':
                _, idx1, dx_obs, dy_obs = constraint
                dx_pred, dy_pred = -params[2*idx1], -params[2*idx1 + 1]
            elif constraint[0] == 'var_to_var':
                _, idx1, idx2, dx_obs, dy_obs = constraint
                dx_pred = params[2*idx2] - params[2*idx1]
                dy_pred = params[2*idx2 + 1] - params[2*idx1 + 1]
            
            residuals.extend([
                weight * (dx_pred - dx_obs),
                weight * (dy_pred - dy_obs)
            ])
        
        return np.array(residuals)


class ImageBlender:
    """图像融合器 - 处理最终图像融合"""
    
    def __init__(self, config):
        self.config = config
    
    def blend_images(self, color_images, final_positions, output_path):
        """融合所有图像 - OpenCV多线程实现"""
        print(f"\n🎨 第四阶段：OpenCV多线程图像融合")
        
        # 设置OpenCV线程数
        cv2.setNumThreads(self.config.num_threads)
        print(f"   🧵 使用 {self.config.num_threads} 个OpenCV线程")
        
        canvas_width, canvas_height, offset_x, offset_y = self._calculate_canvas_size(
            color_images, final_positions
        )
        
        print(f"   📏 画布尺寸：{canvas_width} × {canvas_height}")
        
        # 计算内存需求并选择合适的处理方式
        memory_gb = (canvas_width * canvas_height * 3) / (1024**3)
        print(f"   💾 预估内存需求：{memory_gb:.2f}GB")
        
        if memory_gb > self.config.memory_limit_gb:
            print(f"   ⚠️  内存需求超过限制({self.config.memory_limit_gb}GB)，使用分块输出模式")
            return self._blend_with_streaming_output(
                color_images, final_positions,
                canvas_width, canvas_height, offset_x, offset_y, output_path
            )
        else:
            print(f"   ✅ 内存需求在限制内，使用标准模式")
            return self._blend_opencv_multithread(
                color_images, final_positions,
                canvas_width, canvas_height, offset_x, offset_y, output_path
            )
    
    def _blend_with_streaming_output(self, color_images, final_positions, canvas_width, canvas_height, offset_x, offset_y, output_path):
        """分块输出模式 - 解决大图像内存问题"""
        
        # 自适应调整块大小以避免内存溢出
        max_memory_per_strip = 0.5  # 每个条带最大内存(GB)
        strip_height = int((max_memory_per_strip * 1024**3) / (canvas_width * 3))
        strip_height = min(strip_height, self.config.chunk_size)
        strip_height = max(strip_height, 512)  # 最小条带高度
        
        print(f"   📐 条带高度：{strip_height}像素")
        
        # 创建所有图像的权重图（预计算）
        print("   🎯 预计算权重图...")
        weight_maps = {}
        for (r, c), img in color_images.items():
            weight_maps[(r, c)] = self._create_weight_map(img.shape)
        
        # 创建临时文件来存储各个条带
        import tempfile
        temp_files = []
        
        try:
            # 按条带处理
            num_strips = (canvas_height + strip_height - 1) // strip_height
            print(f"   🔄 处理 {num_strips} 个条带...")
            
            with tqdm(total=num_strips, desc="🎨 分块融合", unit="条带") as pbar:
                for strip_idx in range(num_strips):
                    strip_y_start = strip_idx * strip_height
                    strip_y_end = min(strip_y_start + strip_height, canvas_height)
                    actual_strip_height = strip_y_end - strip_y_start
                    
                    # 处理当前条带
                    strip_canvas = np.zeros((actual_strip_height, canvas_width, 3), dtype=np.float32)
                    strip_weight = np.zeros((actual_strip_height, canvas_width), dtype=np.float32)
                    
                    # 融合与此条带重叠的所有图像
                    for (r, c), (x, y) in final_positions.items():
                        if (r, c) not in color_images:
                            continue
                        
                        img = color_images[(r, c)]
                        weight_map = weight_maps[(r, c)]
                        img_h, img_w = img.shape[:2]
                        
                        img_x = int(x + offset_x)
                        img_y = int(y + offset_y)
                        
                        # 检查是否与当前条带重叠
                        if (img_y + img_h <= strip_y_start or img_y >= strip_y_end):
                            continue
                        
                        # 计算重叠区域
                        overlap_y1 = max(img_y, strip_y_start)
                        overlap_y2 = min(img_y + img_h, strip_y_end)
                        overlap_x1 = max(img_x, 0)
                        overlap_x2 = min(img_x + img_w, canvas_width)
                        
                        if overlap_x2 <= overlap_x1 or overlap_y2 <= overlap_y1:
                            continue
                        
                        # 计算在原图像中的区域
                        img_x1, img_y1 = overlap_x1 - img_x, overlap_y1 - img_y
                        img_x2, img_y2 = overlap_x2 - img_x, overlap_y2 - img_y
                        
                        # 计算在条带中的区域
                        strip_x1, strip_y1 = overlap_x1, overlap_y1 - strip_y_start
                        strip_x2, strip_y2 = overlap_x2, overlap_y2 - strip_y_start
                        
                        # 提取图像和权重区域
                        img_region = img[img_y1:img_y2, img_x1:img_x2].astype(np.float32)
                        weight_region = weight_map[img_y1:img_y2, img_x1:img_x2]
                        
                        # 加权融合
                        for ch in range(3):
                            weighted_img = cv2.multiply(img_region[:, :, ch], weight_region)
                            strip_canvas[strip_y1:strip_y2, strip_x1:strip_x2, ch] = cv2.add(
                                strip_canvas[strip_y1:strip_y2, strip_x1:strip_x2, ch],
                                weighted_img
                            )
                        
                        strip_weight[strip_y1:strip_y2, strip_x1:strip_x2] = cv2.add(
                            strip_weight[strip_y1:strip_y2, strip_x1:strip_x2],
                            weight_region
                        )
                    
                    # 归一化并转换为uint8
                    strip_weight = np.maximum(strip_weight, 1e-10)
                    strip_result = np.zeros((actual_strip_height, canvas_width, 3), dtype=np.uint8)
                    
                    for ch in range(3):
                        normalized_channel = cv2.divide(strip_canvas[:, :, ch], strip_weight)
                        strip_result[:, :, ch] = np.clip(normalized_channel, 0, 255).astype(np.uint8)
                    
                    # 保存条带到临时文件
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.npy')
                    np.save(temp_file.name, strip_result)
                    temp_files.append(temp_file.name)
                    temp_file.close()
                    
                    pbar.update(1)
            
            # 合并所有条带到最终图像 - 使用直接写入避免内存溢出
            print("   🔗 直接写入最终图像...")
            
            # 使用 OpenCV 的图像写入器进行分块写入
            if output_path.lower().endswith('.jpg') or output_path.lower().endswith('.jpeg'):
                # 对于JPEG，需要先创建完整图像（无法避免）
                # 但我们可以尝试使用更少的内存
                try:
                    final_result = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)
                    
                    for strip_idx, temp_file in enumerate(temp_files):
                        strip_y_start = strip_idx * strip_height
                        strip_y_end = min(strip_y_start + strip_height, canvas_height)
                        
                        strip_data = np.load(temp_file)
                        final_result[strip_y_start:strip_y_end] = strip_data
                        
                        # 立即释放strip_data内存
                        del strip_data
                    
                    # 保存最终结果
                    success = cv2.imwrite(output_path, final_result, [cv2.IMWRITE_JPEG_QUALITY, 85])
                    if not success:
                        raise RuntimeError(f"保存图像失败: {output_path}")
                    
                    print(f"   ✅ 分块融合完成，共处理 {num_strips} 个条带")
                    return final_result
                
                except MemoryError:
                    print("   ⚠️  仍然内存不足，尝试PNG格式或进一步减小条带大小")
                    # 如果仍然内存不足，改用PNG格式的分块写入
                    png_output = output_path.replace('.jpg', '.png').replace('.jpeg', '.png')
                    return self._write_strips_as_png(temp_files, strip_height, canvas_height, canvas_width, png_output, num_strips)
            
            else:
                # 对于PNG等格式，可以使用分块写入
                return self._write_strips_as_png(temp_files, strip_height, canvas_height, canvas_width, output_path, num_strips)
            
        finally:
            # 清理临时文件
            for temp_file in temp_files:
                try:
                    os.unlink(temp_file)
                except:
                    pass
    
    def _blend_opencv_multithread(self, color_images, final_positions, canvas_width, canvas_height, offset_x, offset_y, output_path):
        """OpenCV多线程融合实现 - 标准模式"""
        
        # 创建最终画布
        canvas = np.zeros((canvas_height, canvas_width, 3), dtype=np.float32)
        weight_canvas = np.zeros((canvas_height, canvas_width), dtype=np.float32)
        
        # 分块处理大图像
        chunk_size = self.config.chunk_size
        y_chunks = list(range(0, canvas_height, chunk_size))
        x_chunks = list(range(0, canvas_width, chunk_size))
        
        # 创建所有图像的权重图（预计算）
        print("   🎯 预计算权重图...")
        weight_maps = {}
        for (r, c), img in color_images.items():
            weight_maps[(r, c)] = self._create_weight_map(img.shape)
        
        def process_chunk(chunk_y_start, chunk_x_start):
            """处理单个图像块"""
            y_end = min(chunk_y_start + chunk_size, canvas_height)
            x_end = min(chunk_x_start + chunk_size, canvas_width)
            
            chunk_canvas = np.zeros((y_end - chunk_y_start, x_end - chunk_x_start, 3), dtype=np.float32)
            chunk_weight = np.zeros((y_end - chunk_y_start, x_end - chunk_x_start), dtype=np.float32)
            
            # 处理与此块重叠的所有图像
            for (r, c), (x, y) in final_positions.items():
                if (r, c) not in color_images:
                    continue
                
                img = color_images[(r, c)]
                weight_map = weight_maps[(r, c)]
                img_h, img_w = img.shape[:2]
                
                img_x = int(x + offset_x)
                img_y = int(y + offset_y)
                
                # 检查是否与当前块重叠
                if (img_x + img_w <= chunk_x_start or img_x >= x_end or 
                    img_y + img_h <= chunk_y_start or img_y >= y_end):
                    continue
                
                # 计算重叠区域
                overlap_x1 = max(img_x, chunk_x_start)
                overlap_y1 = max(img_y, chunk_y_start)
                overlap_x2 = min(img_x + img_w, x_end)
                overlap_y2 = min(img_y + img_h, y_end)
                
                if overlap_x2 <= overlap_x1 or overlap_y2 <= overlap_y1:
                    continue
                
                # 计算在原图像中的区域
                img_x1, img_y1 = overlap_x1 - img_x, overlap_y1 - img_y
                img_x2, img_y2 = overlap_x2 - img_x, overlap_y2 - img_y
                
                # 计算在块中的区域
                chunk_x1, chunk_y1 = overlap_x1 - chunk_x_start, overlap_y1 - chunk_y_start
                chunk_x2, chunk_y2 = overlap_x2 - chunk_x_start, overlap_y2 - chunk_y_start
                
                # 提取图像和权重区域
                img_region = img[img_y1:img_y2, img_x1:img_x2].astype(np.float32)
                weight_region = weight_map[img_y1:img_y2, img_x1:img_x2]
                
                # 使用OpenCV进行加权融合
                for ch in range(3):
                    weighted_img = cv2.multiply(img_region[:, :, ch], weight_region)
                    chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2, ch] = cv2.add(
                        chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2, ch],
                        weighted_img
                    )
                
                chunk_weight[chunk_y1:chunk_y2, chunk_x1:chunk_x2] = cv2.add(
                    chunk_weight[chunk_y1:chunk_y2, chunk_x1:chunk_x2],
                    weight_region
                )
            
            return (chunk_y_start, chunk_x_start, y_end, x_end), chunk_canvas, chunk_weight
        
        # 并行处理所有块
        print("   🧩 多线程分块融合...")
        total_chunks = len(y_chunks) * len(x_chunks)
        
        with ThreadPoolExecutor(max_workers=self.config.num_threads) as executor:
            futures = []
            for y_start in y_chunks:
                for x_start in x_chunks:
                    future = executor.submit(process_chunk, y_start, x_start)
                    futures.append(future)
            
            # 收集结果并合并到主画布
            with tqdm(total=total_chunks, desc="🔄 合并块", unit="块") as pbar:
                for future in as_completed(futures):
                    (chunk_y_start, chunk_x_start, y_end, x_end), chunk_canvas, chunk_weight = future.result()
                    
                    # 将块合并到主画布
                    canvas[chunk_y_start:y_end, chunk_x_start:x_end] = cv2.add(
                        canvas[chunk_y_start:y_end, chunk_x_start:x_end],
                        chunk_canvas
                    )
                    weight_canvas[chunk_y_start:y_end, chunk_x_start:x_end] = cv2.add(
                        weight_canvas[chunk_y_start:y_end, chunk_x_start:x_end],
                        chunk_weight
                    )
                    
                    pbar.update(1)
        
        # 归一化和最终处理
        print("   🎨 最终归一化处理...")
        result = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)
        
        # 避免除零
        weight_canvas = np.maximum(weight_canvas, 1e-10)
        
        # 使用OpenCV进行归一化
        for ch in range(3):
            normalized_channel = cv2.divide(canvas[:, :, ch], weight_canvas)
            result[:, :, ch] = np.clip(normalized_channel, 0, 255).astype(np.uint8)
        
        # 保存结果
        success = cv2.imwrite(output_path, result, [cv2.IMWRITE_JPEG_QUALITY, 85])
        if not success:
            raise RuntimeError(f"保存图像失败: {output_path}")
        
        print(f"   ✅ 融合完成，使用了 {total_chunks} 个处理块")
        return result
    
    def _create_weight_map(self, img_shape):
        """创建权重图 - 边缘羽化效果"""
        h, w = img_shape[:2]
        
        # 创建距离边缘的距离图
        y_indices, x_indices = np.ogrid[:h, :w]
        
        dist_to_edge = np.minimum(
            np.minimum(y_indices, h - 1 - y_indices),
            np.minimum(x_indices, w - 1 - x_indices)
        )
        
        # 计算羽化像素数
        feather_pixels = min(self.config.max_feather_pixels, min(h, w) // 10)
        feather_pixels = max(feather_pixels, 5)
        
        # 创建权重图 - 使用高斯衰减
        sigma = feather_pixels / 3.0
        weight_map = np.exp(-np.maximum(0, feather_pixels - dist_to_edge) ** 2 / (2 * sigma ** 2))
        weight_map = np.clip(weight_map, 0.1, 1.0)
        
        # 内部区域设为1.0
        inner_mask = dist_to_edge >= feather_pixels
        weight_map[inner_mask] = 1.0
        
        return weight_map.astype(np.float32)
    
    def _calculate_canvas_size(self, color_images, final_positions):
        """计算画布尺寸"""
        sample_img = next(iter(color_images.values()))
        img_height, img_width = sample_img.shape[:2]
        
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        for (r, c), (x, y) in final_positions.items():
            if (r, c) in color_images:
                corners = [
                    (x, y), (x + img_width, y),
                    (x, y + img_height), (x + img_width, y + img_height)
                ]
                for corner_x, corner_y in corners:
                    min_x, max_x = min(min_x, corner_x), max(max_x, corner_x)
                    min_y, max_y = min(min_y, corner_y), max(max_y, corner_y)
        
        canvas_width = int(np.ceil(max_x - min_x))
        canvas_height = int(np.ceil(max_y - min_y))
        offset_x, offset_y = -min_x, -min_y
        
        return canvas_width, canvas_height, offset_x, offset_y
    
    def _write_strips_as_png(self, temp_files, strip_height, canvas_height, canvas_width, output_path, num_strips):
        """分块写入PNG格式 - 进一步降低内存使用"""
        try:
            from PIL import Image
            import io
            
            # 创建一个空的PIL图像
            final_image = Image.new('RGB', (canvas_width, canvas_height), color=(0, 0, 0))
            
            print(f"   📝 逐条带写入PNG...")
            for strip_idx, temp_file in enumerate(temp_files):
                strip_y_start = strip_idx * strip_height
                strip_y_end = min(strip_y_start + strip_height, canvas_height)
                
                # 加载条带数据
                strip_data = np.load(temp_file)
                
                # 转换为PIL图像
                strip_pil = Image.fromarray(strip_data)
                
                # 粘贴到最终图像
                final_image.paste(strip_pil, (0, strip_y_start))
                
                # 立即释放内存
                del strip_data, strip_pil
            
            # 保存PNG图像
            final_image.save(output_path, format='PNG', optimize=True)
            
            # 转换回numpy数组返回（如果内存足够）
            try:
                result_array = np.array(final_image)
                final_image.close()
                return result_array
            except MemoryError:
                print("   ⚠️  无法加载结果到内存，但文件已保存")
                final_image.close()
                # 返回一个小的占位符图像
                return np.zeros((100, 100, 3), dtype=np.uint8)
            
        except ImportError:
            print("   ❌ PIL/Pillow未安装，使用低内存JPEG写入")
            return self._write_strips_low_memory_jpeg(temp_files, strip_height, canvas_height, canvas_width, output_path, num_strips)
        except Exception as e:
            print(f"   ❌ PNG写入失败: {e}，尝试低内存JPEG方案")
            return self._write_strips_low_memory_jpeg(temp_files, strip_height, canvas_height, canvas_width, output_path, num_strips)
    
    def _write_strips_low_memory_jpeg(self, temp_files, strip_height, canvas_height, canvas_width, output_path, num_strips):
        """极低内存的JPEG写入方案"""
        print("   🔧 使用极低内存方案...")
        
        # 进一步减小条带大小，分多次处理
        mini_strip_height = min(strip_height // 4, 256)  # 进一步细分
        
        final_result = None
        
        try:
            # 分段处理
            for segment_start in range(0, canvas_height, mini_strip_height * 4):
                segment_end = min(segment_start + mini_strip_height * 4, canvas_height)
                segment_height = segment_end - segment_start
                
                # 创建小段图像
                segment_img = np.zeros((segment_height, canvas_width, 3), dtype=np.uint8)
                
                # 找到相关的条带文件
                for strip_idx, temp_file in enumerate(temp_files):
                    strip_y_start = strip_idx * strip_height
                    strip_y_end = min(strip_y_start + strip_height, canvas_height)
                    
                    # 检查是否与当前段重叠
                    if strip_y_end <= segment_start or strip_y_start >= segment_end:
                        continue
                    
                    # 计算重叠区域
                    overlap_start = max(strip_y_start, segment_start)
                    overlap_end = min(strip_y_end, segment_end)
                    
                    if overlap_end > overlap_start:
                        # 加载条带数据
                        strip_data = np.load(temp_file)
                        
                        # 计算在条带中的索引
                        strip_offset_start = overlap_start - strip_y_start
                        strip_offset_end = overlap_end - strip_y_start
                        
                        # 计算在段中的索引
                        segment_offset_start = overlap_start - segment_start
                        segment_offset_end = overlap_end - segment_start
                        
                        # 复制数据
                        segment_img[segment_offset_start:segment_offset_end] = strip_data[strip_offset_start:strip_offset_end]
                        
                        del strip_data
                
                # 如果这是最后一段，保存整个图像
                if final_result is None and segment_start == 0:
                    final_result = segment_img
                elif final_result is not None:
                    # 合并到最终结果
                    new_result = np.zeros((segment_end, canvas_width, 3), dtype=np.uint8)
                    new_result[:final_result.shape[0]] = final_result
                    new_result[segment_start:segment_end] = segment_img
                    del final_result
                    final_result = new_result
                
                del segment_img
            
            # 保存结果
            if final_result is not None:
                success = cv2.imwrite(output_path, final_result, [cv2.IMWRITE_JPEG_QUALITY, 85])
                if not success:
                    raise RuntimeError(f"保存图像失败: {output_path}")
                
                print(f"   ✅ 极低内存模式完成，共处理 {num_strips} 个条带")
                return final_result
            else:
                raise RuntimeError("无法创建最终图像")
        
        except Exception as e:
            print(f"   ❌ 极低内存方案也失败: {e}")
            print("   💡 建议：增加系统内存或使用更小的图像")
            raise


class GridStitcher:
    """主拼接器类 - 协调所有组件"""
    
    def __init__(self, config):
        self.config = config
        self.loader = ImageLoader(config)
        self.matcher = PhaseCorrelationMatcher(config)
        self.optimizer = GlobalOptimizer(config)
        self.blender = ImageBlender(config)
    
    def stitch(self, output_path=None):
        """执行完整拼接流程"""
        if output_path is None:
            output_path = f"{self.config.image_dir}_stitched_advanced.jpg"
        
        total_start = time.time()
        
        print("🚀 " + "="*60)
        print("🚀 高性能图像拼接 - OpenCV多线程亚像素配准")
        print("🚀 " + "="*60)
        
        try:
            # 四个主要阶段
            self.loader.load_all_images()
            
            self.matcher.calculate_all_offsets(self.loader.gray_images)
            
            self.optimizer.optimize_positions(
                self.matcher.pairwise_offsets, 
                self.loader.color_images
            )
            
            result_image = self.blender.blend_images(
                self.loader.color_images,
                self.optimizer.final_positions,
                output_path
            )
            
            # 保存配置文件
            self._save_configuration(output_path)
            
            # 显示结果
            total_time = time.time() - total_start
            file_size_mb = os.path.getsize(output_path) / (1024 * 1024)
            
            print("\n🏁 " + "="*60)
            print("🏁 拼接完成!")
            print("🏁 " + "="*60)
            print(f"📊 图像尺寸: {result_image.shape}")
            print(f"🎯 处理图像: {len(self.optimizer.final_positions)}张")
            print(f"💾 文件大小: {file_size_mb:.1f}MB")
            print(f"⏱️  总耗时: {total_time:.2f}s")
            print(f"📸 结果保存至: {output_path}")
            print("🏁 " + "="*60)
            
            return self.optimizer.final_positions, result_image
            
        except Exception as e:
            print(f"❌ 拼接失败: {str(e)}")
            raise
    
    def _save_configuration(self, output_path):
        """保存配置文件"""
        config_file = "TileConfiguration.registered-python.txt"
        
        with open(config_file, 'w') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n\n")
            f.write("# Define the image coordinates (subpixel precision registration by Python)\n")
            
            for r in range(self.config.rows):
                for c in range(self.config.cols):
                    if (r, c) in self.optimizer.final_positions:
                        x, y = self.optimizer.final_positions[(r, c)]
                        f.write(f"r{r:03d}_c{c:03d}.jpg; ; ({x:.6f}, {y:.6f})\n")
        
        print(f"📄 配准文件已保存: {config_file}")


# ========================= 简化的调用接口 =========================

def stitch_from_directory(image_dir, rows, cols, overlap_ratio=0.1, output_path=None):
    """
    从图像目录创建拼接
    
    参数:
        image_dir: 图像文件夹路径
        rows: 网格行数
        cols: 网格列数
        overlap_ratio: 重叠度 (0.1 = 10%)
        output_path: 输出文件路径
    
    返回:
        (final_positions, result_image)
    """
    config = StitchingConfig.from_directory(image_dir, rows, cols, overlap_ratio)
    
    stitcher = GridStitcher(config)
    return stitcher.stitch(output_path)


def stitch_from_config_file(image_dir, tile_config_path, output_path=None):
    """
    从TileConfiguration.txt文件创建拼接
    
    参数:
        image_dir: 图像文件夹路径
        tile_config_path: TileConfiguration.txt文件路径
        output_path: 输出文件路径
    
    返回:
        (final_positions, result_image)
    """
    config = StitchingConfig.from_tile_config(image_dir, tile_config_path)
    
    stitcher = GridStitcher(config)
    return stitcher.stitch(output_path)


def main():
    """简化的主函数"""
    
    # 检查系统内存
    available_memory_gb = psutil.virtual_memory().available / (1024**3)
    print(f"💾 可用系统内存: {available_memory_gb:.1f}GB")
    
    # ====================== 使用示例 ======================
    
     #方式1: 手动指定参数
    #positions, result = stitch_from_directory(
    #     image_dir="test02",
    #     rows=6, cols=16,
    #     overlap_ratio=0.1
    #)
    
    # 方式2: 使用配置文件 (推荐)
    try:
        # 根据可用内存调整配置
        if available_memory_gb < 20:
            print("⚠️  检测到低内存环境，启用超低内存模式")
            config = StitchingConfig.from_tile_config(
                image_dir="Image_55",
                tile_config_path= "Image_55/TileConfiguration.txt"
            )
            # 超低内存配置
            config.memory_limit_gb = 5
            config.chunk_size = 4096
            config.num_threads = multiprocessing.cpu_count() 
            
            stitcher = GridStitcher(config)
            positions, result = stitcher.stitch()
        else:
            positions, result = stitch_from_config_file(
                image_dir="Image_55",
                tile_config_path="Image_55/TileConfiguration.txt"
            )
    
    except Exception as e:
        print(f"❌ 拼接失败: {e}")
        if "memory" in str(e).lower() or "unable to allocate" in str(e).lower():
            print("\n💡 内存不足解决方案:")
            print("   1. 关闭其他应用程序释放内存")
            print("   2. 降低图像分辨率")
            print("   3. 分批处理部分图像")
            print("   4. 增加虚拟内存/交换文件")
        raise
    
    # ===================================================
    
    print(f"\n✅ 拼接完成！处理了 {len(positions)} 张图像")


if __name__ == "__main__":
    main() 