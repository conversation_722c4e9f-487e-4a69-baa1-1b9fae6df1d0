"""
高性能多线程图像拼接工具
=======================

主要特性:
- 🚀 多线程并行处理：图像加载、相位相关匹配、图像融合全程多线程加速
- ⚡ libvips支持：可选择高性能libvips后端进行图像处理
- 🎯 亚像素精度：基于相位相关的亚像素级图像配准
- 🧠 全局优化：最小二乘法消除累积误差
- 💾 内存高效：支持大图像拼接，可调节内存使用策略

性能优化说明:
- 图像融合现已支持多线程预处理，大幅提升处理速度
- libvips后端充分利用多核CPU和内存缓存
- 自动检测最优线程数，也可手动指定
"""

import cv2
import numpy as np
import os
import re
import time
import multiprocessing
from scipy.optimize import least_squares
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import pyvips


class StitchingConfig:
    """拼接配置类 - 统一管理所有参数"""
    
    def __init__(self):
        # 基本参数
        self.image_dir = None
        self.rows = 3
        self.cols = 3
        self.overlap_ratio = 0.1
        self.num_threads = multiprocessing.cpu_count()
        
        # 输入格式
        self.tile_config_path = None
        self.image_naming_format = "r_c"  # "r_c" 或 "s_sequential"
        
        # libvips配置
        self.use_libvips = True
        self.vips_memory_limit_gb = 16
        self.vips_tile_size = 4096
        
        # 融合参数
        self.max_feather_pixels = 50
        self.memory_limit_gb = 30
        
        # 优化参数
        self.confidence_threshold = 0.5
        self.long_distance_weight = 0.3
        self.smoothness_weight = 0.1
    
    def set_thread_count(self, num_threads):
        """设置线程数量"""
        self.num_threads = max(1, min(num_threads, multiprocessing.cpu_count() * 2))
        return self
    
    @classmethod
    def from_directory(cls, image_dir, rows=None, cols=None, overlap_ratio=0.1):
        """从目录创建配置"""
        config = cls()
        config.image_dir = image_dir
        if rows: config.rows = rows
        if cols: config.cols = cols
        config.overlap_ratio = overlap_ratio
        return config
    
    @classmethod
    def from_tile_config(cls, image_dir, tile_config_path):
        """从TileConfiguration.txt文件创建配置"""
        config = cls()
        config.image_dir = image_dir
        config.tile_config_path = tile_config_path
        return config


class ImageLoader:
    """图像加载器 - 处理图像加载和格式解析"""
    
    def __init__(self, config):
        self.config = config
        self.color_images = {}
        self.gray_images = {}
        
    def load_all_images(self):
        """加载所有图像"""
        print(f"\n📋 第一阶段：并行加载图像")
        print(f"   🧵 使用 {self.config.num_threads} 个线程并行加载")
        
        # 构建文件名映射
        grid_to_filename = self._build_filename_mapping()
        
        def load_single_image(r, c):
            """加载单张图像"""
            filename = self._get_filename(r, c, grid_to_filename)
            if not filename:
                return (r, c), None, None, False
                
            filepath = os.path.join(self.config.image_dir, filename)
            if os.path.exists(filepath):
                color_img = cv2.imread(filepath, cv2.IMREAD_COLOR)
                if color_img is not None:
                    gray_img = cv2.cvtColor(color_img, cv2.COLOR_BGR2GRAY)
                    return (r, c), color_img, gray_img, True
            return (r, c), None, None, False
        
        # 并行加载
        start_time = time.time()
        tasks = [(r, c) for r in range(self.config.rows) for c in range(self.config.cols)]
        
        with ThreadPoolExecutor(max_workers=self.config.num_threads) as executor:
            future_to_pos = {executor.submit(load_single_image, r, c): (r, c) for r, c in tasks}
            
            with tqdm(total=len(tasks), desc="🖼️  加载图像", unit="张") as pbar:
                for future in as_completed(future_to_pos):
                    (r, c), color_img, gray_img, success = future.result()
                    if success:
                        self.color_images[(r, c)] = color_img
                        self.gray_images[(r, c)] = gray_img
                    pbar.update(1)
        
        load_time = time.time() - start_time
        print(f"   ✅ 加载完成：{len(self.color_images)} 张图像，耗时 {load_time:.2f}s")
        
        if not self.color_images:
            raise ValueError("❌ 未找到任何图像文件！")
    
    def _build_filename_mapping(self):
        """构建文件名映射"""
        if not self.config.tile_config_path:
            return {}
            
        # 解析TileConfiguration.txt
        grid_info = self._parse_tile_configuration()
        self.config.rows = grid_info['rows']
        self.config.cols = grid_info['cols']
        self.config.overlap_ratio = grid_info['overlap_ratio']
        self.config.image_naming_format = grid_info['naming_format']
        
        return grid_info.get('grid_to_filename', {})
    
    def _parse_tile_configuration(self):
        """解析TileConfiguration.txt文件"""
        config_path = self.config.tile_config_path
        print(f"   📋 解析配置文件: {config_path}")
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        positions = {}
        with open(config_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('#') or line.startswith('dim') or not line:
                    continue
                
                match = re.match(r'(\S+)\s*;\s*;\s*\(([-\d.]+),\s*([-\d.]+)\)', line)
                if match:
                    filename = match.group(1)
                    x = float(match.group(2))
                    y = float(match.group(3))
                    positions[filename] = (x, y)
        
        return self._analyze_grid_structure(positions)
    
    def _analyze_grid_structure(self, positions):
        """分析网格结构"""
        coords = list(positions.values())
        x_coords = sorted(list(set([coord[0] for coord in coords])))
        y_coords = sorted(list(set([coord[1] for coord in coords])))
        
        rows, cols = len(y_coords), len(x_coords)
        
        # 计算重叠度
        if len(x_coords) > 1:
            x_spacing = x_coords[1] - x_coords[0]
            overlap_ratio = max(0, (2448 - x_spacing) / 2448)  # 假设图像宽度2448
        else:
            overlap_ratio = 0.1
        
        # 检测命名格式
        sample_filename = list(positions.keys())[0]
        naming_format = "s_sequential" if sample_filename.startswith('s_') else "r_c"
        
        # 构建网格到文件名的映射
        grid_to_filename = {}
        if naming_format == "s_sequential":
            sorted_files = sorted(positions.keys(), key=lambda x: int(x.split('_')[1].split('.')[0]))
            for idx, filename in enumerate(sorted_files):
                row = idx // cols
                col = (idx % cols) if row % 2 == 0 else (cols - 1 - (idx % cols))
                grid_to_filename[(row, col)] = filename
        
        print(f"   ✅ 检测到 {rows}×{cols} 网格，重叠度 {overlap_ratio*100:.1f}%")
        
        return {
            'rows': rows,
            'cols': cols,
            'overlap_ratio': overlap_ratio,
            'naming_format': naming_format,
            'grid_to_filename': grid_to_filename
        }
    
    def _get_filename(self, r, c, grid_to_filename):
        """获取指定位置的文件名"""
        if grid_to_filename and (r, c) in grid_to_filename:
            return grid_to_filename[(r, c)]
        else:
            return f"r{r:03d}_c{c:03d}.jpg"


class PhaseCorrelationMatcher:
    """相位相关匹配器 - 处理图像配准"""
    
    def __init__(self, config):
        self.config = config
        self.pairwise_offsets = {}
    
    def calculate_all_offsets(self, gray_images):
        """计算所有相邻图像的偏移量"""
        print(f"\n🔍 第二阶段：并行计算相位相关偏移")
        
        # 准备所有图像对
        tasks = []
        for r in range(self.config.rows):
            for c in range(self.config.cols - 1):
                tasks.append(((r, c), (r, c + 1), 'horizontal'))
        for r in range(self.config.rows - 1):
            for c in range(self.config.cols):
                tasks.append(((r, c), (r + 1, c), 'vertical'))
        
        # 并行处理
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=self.config.num_threads) as executor:
            future_to_task = {
                executor.submit(self._calculate_single_offset, task, gray_images): task 
                for task in tasks
            }
            
            valid_count = 0
            with tqdm(total=len(tasks), desc="🔄 相位相关", unit="对") as pbar:
                for future in as_completed(future_to_task):
                    result = future.result()
                    if result:
                        self.pairwise_offsets[result['pair']] = result['offset']
                        valid_count += 1
                    pbar.update(1)
        
        calc_time = time.time() - start_time
        print(f"   ✅ 计算完成：{valid_count}/{len(tasks)} 有效匹配，耗时 {calc_time:.2f}s")
    
    def _calculate_single_offset(self, pair_info, gray_images):
        """计算单个图像对的偏移量"""
        (r1, c1), (r2, c2), direction = pair_info
        
        if (r1, c1) not in gray_images or (r2, c2) not in gray_images:
            return None
        
        img1, img2 = gray_images[(r1, c1)], gray_images[(r2, c2)]
        
        try:
            roi1, roi2 = self._extract_overlap_roi(img1, img2, direction)
            dx, dy, confidence = self._phase_correlation(roi1, roi2)
            
            # 调整到全图坐标系
            if direction == 'horizontal':
                actual_dx = img1.shape[1] - roi1.shape[1] + dx
                actual_dy = dy
            else:
                actual_dx = dx
                actual_dy = img1.shape[0] - roi1.shape[0] + dy
            
            return {
                'pair': ((r1, c1), (r2, c2)),
                'offset': (actual_dx, actual_dy, confidence),
                'direction': direction
            }
        except Exception:
            return None
    
    def _extract_overlap_roi(self, img1, img2, direction):
        """提取重叠区域"""
        h1, w1 = img1.shape
        
        if direction == 'horizontal':
            overlap_width = max(int(w1 * self.config.overlap_ratio), 200)
            roi1 = img1[:, -overlap_width:]
            roi2 = img2[:, :overlap_width]
        else:
            overlap_height = max(int(h1 * self.config.overlap_ratio), 200)
            roi1 = img1[-overlap_height:, :]
            roi2 = img2[:overlap_height, :]
        
        return roi1, roi2
    
    def _phase_correlation(self, img1, img2):
        """相位相关核心算法"""
        def apply_hanning_window(img):
            h, w = img.shape
            hann_h = np.hanning(h).reshape(-1, 1)
            hann_w = np.hanning(w).reshape(1, -1)
            return img.astype(np.float32) * (hann_h * hann_w)
        
        # 确保相同尺寸
        if img1.shape != img2.shape:
            h = min(img1.shape[0], img2.shape[0])
            w = min(img1.shape[1], img2.shape[1])
            img1, img2 = img1[:h, :w], img2[:h, :w]
        
        # 应用窗函数
        img1_windowed = apply_hanning_window(img1)
        img2_windowed = apply_hanning_window(img2)
        
        # FFT计算
        f1 = np.fft.fft2(img1_windowed)
        f2 = np.fft.fft2(img2_windowed)
        
        # 相位相关
        cross_power = f1 * np.conj(f2)
        cross_power_abs = np.abs(cross_power) + 1e-15
        cross_power_norm = cross_power / cross_power_abs
        
        correlation = np.real(np.fft.ifft2(cross_power_norm))
        correlation = np.fft.fftshift(correlation)
        
        # 亚像素峰值检测
        h, w = correlation.shape
        peak_y, peak_x = np.unravel_index(np.argmax(correlation), correlation.shape)
        
        # 抛物线拟合亚像素精化
        if 0 < peak_x < w-1 and 0 < peak_y < h-1:
            # X方向
            c1, c2, c3 = correlation[peak_y, peak_x-1:peak_x+2]
            dx = 0.5 * (c1 - c3) / (c1 + c3 - 2*c2) if abs(c1 + c3 - 2*c2) > 1e-10 else 0
            
            # Y方向
            c1, c2, c3 = correlation[peak_y-1:peak_y+2, peak_x]
            dy = 0.5 * (c1 - c3) / (c1 + c3 - 2*c2) if abs(c1 + c3 - 2*c2) > 1e-10 else 0
        else:
            dx = dy = 0
        
        # 转换到图像坐标系
        center_x, center_y = w // 2, h // 2
        offset_x = peak_x + dx - center_x
        offset_y = peak_y + dy - center_y
        
        # 计算置信度
        max_val = correlation[peak_y, peak_x]
        mask = np.ones_like(correlation, dtype=bool)
        mask[max(0, peak_y-2):peak_y+3, max(0, peak_x-2):peak_x+3] = False
        bg_mean = correlation[mask].mean()
        bg_std = correlation[mask].std()
        confidence = min(1.0, (max_val - bg_mean) / (bg_std + 1e-10) / 20.0)
        
        return offset_x, offset_y, confidence


class GlobalOptimizer:
    """全局优化器 - 消除累积误差"""
    
    def __init__(self, config):
        self.config = config
        self.final_positions = {}
    
    def optimize_positions(self, pairwise_offsets, color_images):
        """全局优化所有图像位置"""
        print(f"\n🎯 第三阶段：全局优化")
        
        # 构建变量映射
        tile_to_idx = {}
        idx_to_tile = {}
        idx = 0
        
        for r in range(self.config.rows):
            for c in range(self.config.cols):
                if (r, c) in color_images and (r, c) != (0, 0):
                    tile_to_idx[(r, c)] = idx
                    idx_to_tile[idx] = (r, c)
                    idx += 1
        
        print(f"   📊 优化变量：{len(tile_to_idx)} 个图像位置")
        
        # 构建约束和权重
        constraints, weights = self._build_constraints(pairwise_offsets, tile_to_idx)
        
        # 初始位置估计
        x0 = self._estimate_initial_positions(pairwise_offsets, tile_to_idx, color_images)
        
        # 执行优化
        start_time = time.time()
        result = least_squares(
            lambda params: self._residuals(params, constraints, weights, tile_to_idx),
            x0, method='trf', loss='soft_l1'
        )
        
        # 提取结果
        self.final_positions = {(0, 0): (0.0, 0.0)}
        if result.success:
            for idx in range(len(tile_to_idx)):
                tile = idx_to_tile[idx]
                self.final_positions[tile] = (result.x[2*idx], result.x[2*idx + 1])
        
        opt_time = time.time() - start_time
        print(f"   ✅ 优化完成，耗时 {opt_time:.3f}s")
    
    def _build_constraints(self, pairwise_offsets, tile_to_idx):
        """构建优化约束"""
        constraints = []
        weights = []
        
        # 相邻图像约束
        for pair, (dx, dy, confidence) in pairwise_offsets.items():
            tile1, tile2 = pair
            
            if tile1 == (0, 0) and tile2 in tile_to_idx:
                constraints.append(('origin_to_var', tile_to_idx[tile2], dx, dy))
                weights.append(confidence)
            elif tile2 == (0, 0) and tile1 in tile_to_idx:
                constraints.append(('var_to_origin', tile_to_idx[tile1], dx, dy))
                weights.append(confidence)
            elif tile1 in tile_to_idx and tile2 in tile_to_idx:
                constraints.append(('var_to_var', tile_to_idx[tile1], tile_to_idx[tile2], dx, dy))
                weights.append(confidence)
        
        # 添加长距离约束
        long_distance_count = self._add_long_distance_constraints(
            constraints, weights, pairwise_offsets, tile_to_idx
        )
        
        print(f"   ➕ 添加了 {long_distance_count} 个长距离约束")
        return constraints, weights
    
    def _add_long_distance_constraints(self, constraints, weights, pairwise_offsets, tile_to_idx):
        """添加长距离约束减少累积误差"""
        count = 0
        
        # 水平长距离连接
        for r in range(self.config.rows):
            for c1 in range(self.config.cols):
                for step in [2, 3]:
                    c2 = c1 + step
                    if (c2 < self.config.cols and 
                        (r, c1) in tile_to_idx and (r, c2) in tile_to_idx):
                        
                        # 计算累积偏移
                        est_dx, est_dy = 0, 0
                        valid = True
                        
                        for i in range(step):
                            if ((r, c1+i), (r, c1+i+1)) in pairwise_offsets:
                                dx, dy, _ = pairwise_offsets[((r, c1+i), (r, c1+i+1))]
                                est_dx += dx
                                est_dy += dy
                            else:
                                valid = False
                                break
                        
                        if valid:
                            constraints.append((
                                'var_to_var', 
                                tile_to_idx[(r, c1)], 
                                tile_to_idx[(r, c2)], 
                                est_dx, est_dy
                            ))
                            weights.append(self.config.long_distance_weight)
                            count += 1
        
        # 垂直长距离连接
        for c in range(self.config.cols):
            for r1 in range(self.config.rows):
                for step in [2, 3]:
                    r2 = r1 + step
                    if (r2 < self.config.rows and 
                        (r1, c) in tile_to_idx and (r2, c) in tile_to_idx):
                        
                        est_dx, est_dy = 0, 0
                        valid = True
                        
                        for i in range(step):
                            if ((r1+i, c), (r1+i+1, c)) in pairwise_offsets:
                                dx, dy, _ = pairwise_offsets[((r1+i, c), (r1+i+1, c))]
                                est_dx += dx
                                est_dy += dy
                            else:
                                valid = False
                                break
                        
                        if valid:
                            constraints.append((
                                'var_to_var', 
                                tile_to_idx[(r1, c)], 
                                tile_to_idx[(r2, c)], 
                                est_dx, est_dy
                            ))
                            weights.append(self.config.long_distance_weight)
                            count += 1
        
        return count
    
    def _estimate_initial_positions(self, pairwise_offsets, tile_to_idx, color_images):
        """估计初始位置"""
        initial_positions = {(0, 0): (0.0, 0.0)}
        
        # 从原点开始扩展
        for pair, (dx, dy, _) in pairwise_offsets.items():
            tile1, tile2 = pair
            if tile1 == (0, 0) and tile2 in color_images:
                initial_positions[tile2] = (dx, dy)
            elif tile2 == (0, 0) and tile1 in color_images:
                initial_positions[tile1] = (-dx, -dy)
        
        # 逐步扩展
        while True:
            new_positions = {}
            for pair, (dx, dy, _) in pairwise_offsets.items():
                tile1, tile2 = pair
                
                if tile1 in initial_positions and tile2 not in initial_positions:
                    x1, y1 = initial_positions[tile1]
                    new_positions[tile2] = (x1 + dx, y1 + dy)
                elif tile2 in initial_positions and tile1 not in initial_positions:
                    x2, y2 = initial_positions[tile2]
                    new_positions[tile1] = (x2 - dx, y2 - dy)
            
            if not new_positions:
                break
            initial_positions.update(new_positions)
        
        # 构建参数向量
        x0 = np.zeros(2 * len(tile_to_idx))
        for tile, (x, y) in initial_positions.items():
            if tile in tile_to_idx:
                idx = tile_to_idx[tile]
                x0[2*idx] = x
                x0[2*idx + 1] = y
        
        return x0
    
    def _residuals(self, params, constraints, weights, tile_to_idx):
        """计算残差"""
        residuals = []
        
        for i, constraint in enumerate(constraints):
            weight = weights[i]
            
            if constraint[0] == 'origin_to_var':
                _, idx2, dx_obs, dy_obs = constraint
                dx_pred, dy_pred = params[2*idx2], params[2*idx2 + 1]
            elif constraint[0] == 'var_to_origin':
                _, idx1, dx_obs, dy_obs = constraint
                dx_pred, dy_pred = -params[2*idx1], -params[2*idx1 + 1]
            elif constraint[0] == 'var_to_var':
                _, idx1, idx2, dx_obs, dy_obs = constraint
                dx_pred = params[2*idx2] - params[2*idx1]
                dy_pred = params[2*idx2 + 1] - params[2*idx1 + 1]
            
            residuals.extend([
                weight * (dx_pred - dx_obs),
                weight * (dy_pred - dy_obs)
            ])
        
        return np.array(residuals)


class ImageBlender:
    """图像融合器 - 处理最终图像融合"""
    
    def __init__(self, config):
        self.config = config
    
    def blend_images(self, color_images, final_positions, output_path):
        """融合所有图像"""
        print(f"\n🎨 第四阶段：智能图像融合")
        
        canvas_width, canvas_height, offset_x, offset_y = self._calculate_canvas_size(
            color_images, final_positions
        )
        
        print(f"   📏 画布尺寸：{canvas_width} × {canvas_height}")
        
        if self.config.use_libvips:
            try:
                print(f"   ⚡ 使用libvips高效融合")
                return self._blend_vips(
                    color_images, final_positions, 
                    canvas_width, canvas_height, offset_x, offset_y, output_path
                )
            except Exception as e:
                print(f"   ⚠️  libvips融合失败，切换到CPU模式: {str(e)}")
                return self._blend_cpu(
                    color_images, final_positions,
                    canvas_width, canvas_height, offset_x, offset_y, output_path
                )
        else:
            print(f"   💾 使用CPU内存高效融合")
            return self._blend_cpu(
                color_images, final_positions,
                canvas_width, canvas_height, offset_x, offset_y, output_path
            )
    
    def _calculate_canvas_size(self, color_images, final_positions):
        """计算画布尺寸"""
        sample_img = next(iter(color_images.values()))
        img_height, img_width = sample_img.shape[:2]
        
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        for (r, c), (x, y) in final_positions.items():
            if (r, c) in color_images:
                corners = [
                    (x, y), (x + img_width, y),
                    (x, y + img_height), (x + img_width, y + img_height)
                ]
                for corner_x, corner_y in corners:
                    min_x, max_x = min(min_x, corner_x), max(max_x, corner_x)
                    min_y, max_y = min(min_y, corner_y), max(max_y, corner_y)
        
        canvas_width = int(np.ceil(max_x - min_x))
        canvas_height = int(np.ceil(max_y - min_y))
        offset_x, offset_y = -min_x, -min_y
        
        return canvas_width, canvas_height, offset_x, offset_y
    
    def _create_weight_map(self, img_shape):
        """创建权重图"""
        h, w = img_shape[:2]
        y_indices, x_indices = np.ogrid[:h, :w]
        
        dist_to_edge = np.minimum(
            np.minimum(y_indices, h - 1 - y_indices),
            np.minimum(x_indices, w - 1 - x_indices)
        )
        
        feather_pixels = min(self.config.max_feather_pixels, min(h, w) // 10)
        feather_pixels = max(feather_pixels, 5)
        
        sigma = feather_pixels / 3.0
        weight_map = np.exp(-((feather_pixels - dist_to_edge) ** 2) / (2 * sigma ** 2))
        weight_map = np.clip(weight_map, 0.1, 1.0)
        
        inner_mask = dist_to_edge >= feather_pixels
        weight_map[inner_mask] = 1.0
        
        return weight_map.astype(np.float32)
    
    def _blend_vips(self, color_images, final_positions, canvas_width, canvas_height, offset_x, offset_y, output_path):
        """libvips多线程加速融合"""
        # 设置libvips内存限制和多线程
        pyvips.cache_set_max_mem(int(self.config.vips_memory_limit_gb * 1024 * 1024 * 1024))
        pyvips.cache_set_max(500)
        
        # 创建空白画布
        canvas = pyvips.Image.black(canvas_width, canvas_height, bands=3)
        weight_canvas = pyvips.Image.black(canvas_width, canvas_height, bands=1)
        
        print(f"   📦 libvips内存限制: {self.config.vips_memory_limit_gb:.1f}GB")
        print(f"   🧵 使用 {self.config.num_threads} 个线程并行处理")
        
        # 第一阶段：多线程预处理所有图像
        def preprocess_image(item):
            """预处理单张图像"""
            (r, c), (x, y) = item
            
            if (r, c) not in color_images:
                return None
            
            img = color_images[(r, c)]
            h, w = img.shape[:2]
            
            x_adj = int(x + offset_x)
            y_adj = int(y + offset_y)
            
            # 跳过超出画布的图像
            if (x_adj >= canvas_width or y_adj >= canvas_height or 
                x_adj + w <= 0 or y_adj + h <= 0):
                return None
            
            try:
                # 转换为libvips图像 (BGR转RGB)
                img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                vips_img = pyvips.Image.new_from_array(img_rgb)
                
                # 创建权重图
                weight_map = self._create_weight_map(img.shape)
                vips_weight = pyvips.Image.new_from_array(weight_map)
                
                # 应用权重到图像的每个通道
                weighted_img = vips_img.multiply(vips_weight)
                
                # 计算需要裁剪和嵌入的区域
                crop_x = max(0, -x_adj)
                crop_y = max(0, -y_adj)
                embed_x = max(0, x_adj)
                embed_y = max(0, y_adj)
                
                # 计算实际可用的宽度和高度
                available_w = min(w - crop_x, canvas_width - embed_x)
                available_h = min(h - crop_y, canvas_height - embed_y)
                
                if available_w > 0 and available_h > 0:
                    # 裁剪图像和权重到有效区域
                    if crop_x > 0 or crop_y > 0 or available_w < w or available_h < h:
                        cropped_img = weighted_img.crop(crop_x, crop_y, available_w, available_h)
                        cropped_weight = vips_weight.crop(crop_x, crop_y, available_w, available_h)
                    else:
                        cropped_img = weighted_img
                        cropped_weight = vips_weight
                    
                    # 嵌入到画布大小的图像
                    embedded_img = cropped_img.embed(
                        embed_x, embed_y, canvas_width, canvas_height,
                        extend=pyvips.Extend.BLACK
                    )
                    embedded_weight = cropped_weight.embed(
                        embed_x, embed_y, canvas_width, canvas_height,
                        extend=pyvips.Extend.BLACK
                    )
                    
                    return {
                        'position': (r, c),
                        'embedded_img': embedded_img,
                        'embedded_weight': embedded_weight
                    }
                else:
                    return None
                    
            except Exception as e:
                print(f"   ⚠️  预处理图像 ({r},{c}) 时出错: {str(e)}")
                return None
        
        # 并行预处理所有图像
        processed_images = []
        with ThreadPoolExecutor(max_workers=self.config.num_threads) as executor:
            future_to_item = {
                executor.submit(preprocess_image, item): item 
                for item in final_positions.items()
            }
            
            with tqdm(total=len(final_positions), desc="🔄 预处理图像", unit="张") as pbar:
                for future in as_completed(future_to_item):
                    result = future.result()
                    if result is not None:
                        processed_images.append(result)
                    pbar.update(1)
        
        print(f"   ✅ 预处理完成：{len(processed_images)} 张有效图像")
        
        # 第二阶段：串行累积到画布（保证原子性）
        with tqdm(total=len(processed_images), desc="🎨 累积画布", unit="张") as pbar:
            for item in processed_images:
                try:
                    # 累积到画布
                    canvas = canvas.add(item['embedded_img'])
                    weight_canvas = weight_canvas.add(item['embedded_weight'])
                except Exception as e:
                    print(f"   ⚠️  累积图像 {item['position']} 时出错: {str(e)}")
                
                pbar.update(1)
        
        # 归一化：避免除零
        weight_canvas = weight_canvas.add(1e-10)
        
        # 对每个通道进行归一化
        normalized_channels = []
        for band in range(3):
            channel = canvas.extract_band(band)
            normalized_channel = channel.divide(weight_canvas)
            normalized_channels.append(normalized_channel)
        
        # 合并通道
        result_vips = normalized_channels[0].bandjoin(normalized_channels[1:])
        
        # 转换为8位并保存
        result_vips = result_vips.cast(pyvips.BandFormat.UCHAR)
        
        # 保存文件
        if output_path.lower().endswith(('.jpg', '.jpeg')):
            result_vips.write_to_file(output_path, Q=85)
        else:
            result_vips.write_to_file(output_path)
        
        # 转换为numpy数组以返回（可选）
        try:
            result_array = result_vips.numpy()
            # RGB转BGR用于OpenCV
            result_array = cv2.cvtColor(result_array, cv2.COLOR_RGB2BGR)
            return result_array
        except Exception:
            # 如果图像太大无法转换为numpy，返回形状信息
            print(f"   💾 图像太大，无法转换为numpy数组")
            return {"shape": (canvas_height, canvas_width, 3), "path": output_path}
    
    def _blend_cpu(self, color_images, final_positions, canvas_width, canvas_height, offset_x, offset_y, output_path):
        """CPU多线程内存高效融合"""
        canvas = np.zeros((canvas_height, canvas_width, 3), dtype=np.float32)
        weight_canvas = np.zeros((canvas_height, canvas_width), dtype=np.float32)
        
        print(f"   🧵 使用 {self.config.num_threads} 个线程并行处理")
        
        # 第一阶段：多线程预处理所有图像
        def preprocess_image_cpu(item):
            """CPU预处理单张图像"""
            (r, c), (x, y) = item
            
            if (r, c) not in color_images:
                return None
            
            img = color_images[(r, c)]
            h, w = img.shape[:2]
            
            x_adj = int(x + offset_x)
            y_adj = int(y + offset_y)
            weight_map = self._create_weight_map(img.shape)
            
            # 计算覆盖区域
            x_start, y_start = max(0, x_adj), max(0, y_adj)
            x_end, y_end = min(x_adj + w, canvas_width), min(y_adj + h, canvas_height)
            
            if x_start < x_end and y_start < y_end:
                # 计算有效区域
                img_x_start, img_y_start = max(0, -x_adj), max(0, -y_adj)
                img_x_end = img_x_start + (x_end - x_start)
                img_y_end = img_y_start + (y_end - y_start)
                
                img_region = img[img_y_start:img_y_end, img_x_start:img_x_end]
                weight_region = weight_map[img_y_start:img_y_end, img_x_start:img_x_end]
                
                # 预计算加权图像
                weighted_img_region = np.zeros_like(img_region, dtype=np.float32)
                for c_idx in range(3):
                    weighted_img_region[:, :, c_idx] = (
                        img_region[:, :, c_idx].astype(np.float32) * weight_region
                    )
                
                return {
                    'position': (r, c),
                    'canvas_coords': (x_start, y_start, x_end, y_end),
                    'weighted_img': weighted_img_region,
                    'weight_region': weight_region
                }
            else:
                return None
        
        # 并行预处理所有图像
        processed_images = []
        with ThreadPoolExecutor(max_workers=self.config.num_threads) as executor:
            future_to_item = {
                executor.submit(preprocess_image_cpu, item): item 
                for item in final_positions.items()
            }
            
            with tqdm(total=len(final_positions), desc="🔄 预处理图像", unit="张") as pbar:
                for future in as_completed(future_to_item):
                    result = future.result()
                    if result is not None:
                        processed_images.append(result)
                    pbar.update(1)
        
        print(f"   ✅ 预处理完成：{len(processed_images)} 张有效图像")
        
        # 第二阶段：串行累积到画布（保证原子性）
        with tqdm(total=len(processed_images), desc="🎨 累积画布", unit="张") as pbar:
            for item in processed_images:
                try:
                    x_start, y_start, x_end, y_end = item['canvas_coords']
                    
                    # 累积到画布
                    for c_idx in range(3):
                        canvas[y_start:y_end, x_start:x_end, c_idx] += item['weighted_img'][:, :, c_idx]
                    weight_canvas[y_start:y_end, x_start:x_end] += item['weight_region']
                    
                except Exception as e:
                    print(f"   ⚠️  累积图像 {item['position']} 时出错: {str(e)}")
                
                pbar.update(1)
        
        # 归一化
        mask = weight_canvas > 0
        result = np.zeros_like(canvas, dtype=np.uint8)
        for c_idx in range(3):
            result[:, :, c_idx][mask] = (canvas[:, :, c_idx][mask] / weight_canvas[mask]).astype(np.uint8)
        
        cv2.imwrite(output_path, result, [cv2.IMWRITE_JPEG_QUALITY, 85])
        return result


class GridStitcher:
    """主拼接器类 - 协调所有组件"""
    
    def __init__(self, config):
        self.config = config
        self.loader = ImageLoader(config)
        self.matcher = PhaseCorrelationMatcher(config)
        self.optimizer = GlobalOptimizer(config)
        self.blender = ImageBlender(config)
    
    def stitch(self, output_path=None):
        """执行完整拼接流程"""
        if output_path is None:
            output_path = f"{self.config.image_dir}_stitched_advanced.jpg"
        
        total_start = time.time()
        
        print("🚀 " + "="*60)
        print("🚀 高性能图像拼接 - 亚像素精度配准")
        print("🚀 " + "="*60)
        
        try:
            # 四个主要阶段
            self.loader.load_all_images()
            
            self.matcher.calculate_all_offsets(self.loader.gray_images)
            
            self.optimizer.optimize_positions(
                self.matcher.pairwise_offsets, 
                self.loader.color_images
            )
            
            result_image = self.blender.blend_images(
                self.loader.color_images,
                self.optimizer.final_positions,
                output_path
            )
            
            # 保存配置文件
            self._save_configuration(output_path)
            
            # 显示结果
            total_time = time.time() - total_start
            file_size_mb = os.path.getsize(output_path) / (1024 * 1024)
            
            print("\n🏁 " + "="*60)
            print("🏁 拼接完成!")
            print("🏁 " + "="*60)
            print(f"📊 图像尺寸: {result_image.shape}")
            print(f"🎯 处理图像: {len(self.optimizer.final_positions)}张")
            print(f"💾 文件大小: {file_size_mb:.1f}MB")
            print(f"⏱️  总耗时: {total_time:.2f}s")
            print(f"📸 结果保存至: {output_path}")
            print("🏁 " + "="*60)
            
            return self.optimizer.final_positions, result_image
            
        except Exception as e:
            print(f"❌ 拼接失败: {str(e)}")
            raise
    
    def _save_configuration(self, output_path):
        """保存配置文件"""
        config_file = "TileConfiguration.registered-python.txt"
        
        with open(config_file, 'w') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n\n")
            f.write("# Define the image coordinates (subpixel precision registration by Python)\n")
            
            for r in range(self.config.rows):
                for c in range(self.config.cols):
                    if (r, c) in self.optimizer.final_positions:
                        x, y = self.optimizer.final_positions[(r, c)]
                        f.write(f"r{r:03d}_c{c:03d}.jpg; ; ({x:.6f}, {y:.6f})\n")
        
        print(f"📄 配准文件已保存: {config_file}")


# ========================= 简化的调用接口 =========================

def stitch_from_directory(image_dir, rows, cols, overlap_ratio=0.1, use_libvips=True, num_threads=None, output_path=None):
    """
    从图像目录创建拼接
    
    参数:
        image_dir: 图像文件夹路径
        rows: 网格行数
        cols: 网格列数
        overlap_ratio: 重叠度 (0.1 = 10%)
        use_libvips: 是否使用libvips加速 (推荐)
        num_threads: 线程数 (None=自动检测)
        output_path: 输出文件路径
    
    返回:
        (final_positions, result_image)
    """
    config = StitchingConfig.from_directory(image_dir, rows, cols, overlap_ratio)
    config.use_libvips = use_libvips
    if num_threads is not None:
        config.set_thread_count(num_threads)
    
    stitcher = GridStitcher(config)
    return stitcher.stitch(output_path)


def stitch_from_config_file(image_dir, tile_config_path, use_libvips=True, num_threads=None, output_path=None):
    """
    从TileConfiguration.txt文件创建拼接
    
    参数:
        image_dir: 图像文件夹路径
        tile_config_path: TileConfiguration.txt文件路径
        use_libvips: 是否使用libvips加速 (推荐)
        num_threads: 线程数 (None=自动检测)
        output_path: 输出文件路径
    
    返回:
        (final_positions, result_image)
    """
    config = StitchingConfig.from_tile_config(image_dir, tile_config_path)
    config.use_libvips = use_libvips
    if num_threads is not None:
        config.set_thread_count(num_threads)
    
    stitcher = GridStitcher(config)
    return stitcher.stitch(output_path)


def main():
    """简化的主函数 - 现已支持多线程加速！"""
    
    # ====================== 使用示例 ======================
    
    # 方式1: 手动指定参数 (现已支持多线程!)
    positions, result = stitch_from_directory(
        image_dir="test02",
        rows=6, cols=16,
        overlap_ratio=0.1,
        use_libvips=True,  # libvips多线程加速 (推荐)
        num_threads=20      # 指定线程数 (None=自动检测)
    )
    
    # 方式2: 使用配置文件 (推荐)
    #positions, result = stitch_from_config_file(
    #    image_dir="Image_SX88",
    #    tile_config_path="Image_SX88/TileConfiguration.txt",
    #    use_libvips=True,  # libvips多线程加速
    #    num_threads=None   # 自动检测最优线程数
    #)
    
    # 方式3: 高性能配置 (大图像推荐)
    #config = StitchingConfig.from_directory("test02", 6, 16)
    #config.use_libvips = True
    #config.set_thread_count(12)  # 手动设置线程数
    #config.vips_memory_limit_gb = 32  # 增加内存限制
    #stitcher = GridStitcher(config)
    #positions, result = stitcher.stitch()
    
    # ===================================================
    
    print(f"\n✅ 多线程拼接完成！处理了 {len(positions)} 张图像")


if __name__ == "__main__":
    main() 