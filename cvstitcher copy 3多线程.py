import cv2
import numpy as np
import os
from scipy.optimize import least_squares
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from tqdm import tqdm

class CVStitcher:
    def __init__(self, image_dir, rows=3, cols=3, overlap_ratio=0.1, num_threads=16):
        self.image_dir = image_dir
        self.rows = rows
        self.cols = cols
        self.overlap_ratio = overlap_ratio
        self.num_threads = num_threads
        self.images = {}  # 存储彩色图像
        self.gray_images = {}  # 存储灰度图像用于相位相关计算
        self.image_positions = {}
        self.global_offsets = {}
        
    def load_images_parallel(self):
        """并行加载所有图片（彩色和灰度版本）"""
        def load_single_image(r, c):
            filename = f"r{r:03d}_c{c:03d}.jpg"
            filepath = os.path.join(self.image_dir, filename)
            if os.path.exists(filepath):
                # 加载彩色图像
                color_img = cv2.imread(filepath, cv2.IMREAD_COLOR)
                if color_img is not None:
                    # 转换为灰度图像用于相位相关计算
                    gray_img = cv2.cvtColor(color_img, cv2.COLOR_BGR2GRAY)
                    return (r, c), color_img, gray_img, f"Loaded: {filename}, shape: {color_img.shape}"
                else:
                    return (r, c), None, None, f"Failed to load: {filename}"
            else:
                return (r, c), None, None, f"File not found: {filename}"
        
        print(f"Loading images with {self.num_threads} threads...")
        start_time = time.time()
        
        # 创建所有加载任务
        tasks = [(r, c) for r in range(self.rows) for c in range(self.cols)]
        
        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            # 提交所有任务
            future_to_pos = {executor.submit(load_single_image, r, c): (r, c) for r, c in tasks}
            
            # 使用tqdm显示进度
            completed_count = 0
            with tqdm(total=len(tasks), desc="Loading images", unit="img") as pbar:
                for future in as_completed(future_to_pos):
                    (r, c), color_img, gray_img, message = future.result()
                    
                    if color_img is not None:
                        self.images[(r, c)] = color_img
                        self.gray_images[(r, c)] = gray_img
                    
                    completed_count += 1
                    if completed_count % 30 == 0 or completed_count == len(tasks):
                        pbar.set_postfix({'loaded': len(self.images)})
                    pbar.update(1)
        
        load_time = time.time() - start_time
        print(f"✅ Loaded {len(self.images)} images in {load_time:.2f} seconds")
    
    # 保持原有的单个方法不变，用于兼容
    def load_images(self):
        """加载所有图片（兼容性方法，调用并行版本）"""
        self.load_images_parallel()
    
    def apply_hanning_window(self, img):
        """应用汉宁窗以减少边界效应"""
        h, w = img.shape
        hann_h = np.hanning(h).reshape(-1, 1)
        hann_w = np.hanning(w).reshape(1, -1)
        hann_2d = hann_h * hann_w
        return img.astype(np.float32) * hann_2d
    
    def subpixel_peak_refinement(self, correlation_map):
        """亚像素峰值精化"""
        # 找到整数峰值位置
        peak_y, peak_x = np.unravel_index(np.argmax(correlation_map), correlation_map.shape)
        h, w = correlation_map.shape
        
        # 检查边界
        if peak_x == 0 or peak_x == w-1 or peak_y == 0 or peak_y == h-1:
            return peak_x, peak_y
        
        # 使用抛物线拟合进行亚像素精化
        # X方向的亚像素精化
        c1 = correlation_map[peak_y, peak_x-1]
        c2 = correlation_map[peak_y, peak_x]
        c3 = correlation_map[peak_y, peak_x+1]
        
        if c1 + c3 - 2*c2 != 0:
            dx = 0.5 * (c1 - c3) / (c1 + c3 - 2*c2)
        else:
            dx = 0
        
        # Y方向的亚像素精化
        c1 = correlation_map[peak_y-1, peak_x]
        c2 = correlation_map[peak_y, peak_x]
        c3 = correlation_map[peak_y+1, peak_x]
        
        if c1 + c3 - 2*c2 != 0:
            dy = 0.5 * (c1 - c3) / (c1 + c3 - 2*c2)
        else:
            dy = 0
        
        return peak_x + dx, peak_y + dy
    
    def enhanced_phase_correlation(self, img1, img2, use_hanning=True):
        """增强版相位相关，提供亚像素精度（使用灰度图像）"""
        # 确保图片大小相同
        h1, w1 = img1.shape
        h2, w2 = img2.shape
        h = min(h1, h2)
        w = min(w1, w2)
        
        img1_crop = img1[:h, :w]
        img2_crop = img2[:h, :w]
        
        # 应用汉宁窗
        if use_hanning:
            img1_windowed = self.apply_hanning_window(img1_crop)
            img2_windowed = self.apply_hanning_window(img2_crop)
        else:
            img1_windowed = img1_crop.astype(np.float32)
            img2_windowed = img2_crop.astype(np.float32)
        
        # 计算FFT
        f1 = np.fft.fft2(img1_windowed)
        f2 = np.fft.fft2(img2_windowed)
        
        # 计算交叉功率谱
        cross_power_spectrum = f1 * np.conj(f2)
        cross_power_spectrum /= (np.abs(cross_power_spectrum) + 1e-10)
        
        # 计算相位相关
        correlation = np.real(np.fft.ifft2(cross_power_spectrum))
        
        # 亚像素峰值精化
        peak_x_sub, peak_y_sub = self.subpixel_peak_refinement(correlation)
        
        # 正确处理周期性偏移 - 修正符号问题
        # 如果峰值在后半部分，说明实际偏移是负的
        if peak_y_sub > h // 2:
            peak_y_sub = peak_y_sub - h
        if peak_x_sub > w // 2:
            peak_x_sub = peak_x_sub - w
        
        # 计算置信度（使用峰值和其邻域的比值）
        max_val = np.max(correlation)
        mean_val = np.mean(correlation)
        confidence = (max_val - mean_val) / (max_val + 1e-10)
        
        # 返回偏移量：img2相对于img1的偏移
        # 相位相关直接给出的就是img2相对于img1的偏移，不需要取负号
        return peak_x_sub, peak_y_sub, confidence
    
    def get_optimal_overlap_roi(self, img1, img2, direction):
        """获取最优重叠区域（使用灰度图像）"""
        h1, w1 = img1.shape
        h2, w2 = img2.shape
        
        # 从ImageJ日志推断实际重叠度
        if direction == 'horizontal':
            # ImageJ显示水平步长约2181，图片宽度2448
            estimated_overlap = (2448 - 2181) / 2448
            overlap_width = int(w1 * estimated_overlap)
            # 确保重叠区域不会太小
            overlap_width = max(overlap_width, 200)
            roi1 = img1[:, -overlap_width:]
            roi2 = img2[:, :overlap_width]
        elif direction == 'vertical':
            # 垂直方向使用类似的逻辑
            estimated_overlap = (2048 - 1830) / 2048  # 从日志估算
            overlap_height = int(h1 * estimated_overlap)
            overlap_height = max(overlap_height, 200)
            roi1 = img1[-overlap_height:, :]
            roi2 = img2[:overlap_height, :]
        else:
            raise ValueError("Direction must be 'horizontal' or 'vertical'")
        
        return roi1, roi2
    
    def calculate_single_pair_offset(self, pair_info):
        """计算单个图片对的偏移（用于并行处理）"""
        (r1, c1), (r2, c2), direction = pair_info
        
        if (r1, c1) not in self.gray_images or (r2, c2) not in self.gray_images:
            return None
        
        img1 = self.gray_images[(r1, c1)]
        img2 = self.gray_images[(r2, c2)]
        
        try:
            roi1, roi2 = self.get_optimal_overlap_roi(img1, img2, direction)
            dx, dy, confidence = self.enhanced_phase_correlation(roi1, roi2)
            
            # 调整偏移量（考虑ROI位置）
            if direction == 'horizontal':
                actual_dx = img1.shape[1] - roi1.shape[1] + dx
                actual_dy = dy
            else:  # vertical
                actual_dx = dx
                actual_dy = img1.shape[0] - roi1.shape[0] + dy
            
            result = {
                'pair': ((r1, c1), (r2, c2)),
                'offset': (actual_dx, actual_dy, confidence),
                'message': f"  {direction} ({r1},{c1})->({r2},{c2}): dx={actual_dx:.4f}, dy={actual_dy:.4f}, conf={confidence:.6f}"
            }
            return result
        except Exception as e:
            return {
                'pair': ((r1, c1), (r2, c2)),
                'offset': None,
                'message': f"  ERROR ({r1},{c1})->({r2},{c2}): {str(e)}"
            }
    
    def calculate_pairwise_offsets_parallel(self):
        """并行计算相邻图片之间的偏移"""
        print(f"\nCalculating pairwise offsets with {self.num_threads} threads...")
        start_time = time.time()
        
        # 准备所有需要处理的图片对
        tasks = []
        
        # 水平方向的图片对
        for r in range(self.rows):
            for c in range(self.cols - 1):
                tasks.append(((r, c), (r, c + 1), 'horizontal'))
        
        # 垂直方向的图片对
        for r in range(self.rows - 1):
            for c in range(self.cols):
                tasks.append(((r, c), (r + 1, c), 'vertical'))
        
        print(f"Processing {len(tasks)} image pairs...")
        
        pairwise_offsets = {}
        
        # 并行处理所有图片对
        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            # 提交所有任务
            future_to_task = {executor.submit(self.calculate_single_pair_offset, task): task for task in tasks}
            
            # 使用tqdm显示进度
            completed = 0
            valid_count = 0
            with tqdm(total=len(tasks), desc="Phase correlation", unit="pair") as pbar:
                for future in as_completed(future_to_task):
                    result = future.result()
                    if result and result['offset'] is not None:
                        pairwise_offsets[result['pair']] = result['offset']
                        valid_count += 1
                    
                    completed += 1
                    if completed % 30 == 0 or completed == len(tasks):
                        pbar.set_postfix({'valid': valid_count, 'total': completed})
                    pbar.update(1)
        
        calc_time = time.time() - start_time
        print(f"✅ Calculated {len(pairwise_offsets)} valid offsets in {calc_time:.2f} seconds")
        print(f"⚡ Average time per pair: {calc_time/len(tasks):.3f} seconds")
        
        return pairwise_offsets
    
    # 保持原有方法用于兼容性
    def calculate_pairwise_offsets(self):
        """计算相邻图片之间的偏移（兼容性方法，调用并行版本）"""
        return self.calculate_pairwise_offsets_parallel()
    
    def blend_images_memory_efficient(self, positions, output_path="stitched_result.jpg"):
        """内存高效的图像融合（逐个处理，避免OOM）- 针对32GB内存优化"""
        print(f"\nMemory-efficient blending...")
        start_time = time.time()
        
        # 计算画布尺寸
        canvas_width, canvas_height, offset_x, offset_y = self.calculate_canvas_size(positions)
        
        # 检查内存需求并可能降采样
        memory_gb = (canvas_width * canvas_height * 3 * 8) / (1024**3)  # float64
        print(f"Estimated memory requirement: {memory_gb:.2f} GB")
        
        # 针对32GB内存调整：如果内存需求超过16GB，进行降采样
        scale_factor = 1.0
        if memory_gb > 16.0:  # 提高阈值，充分利用32GB内存
            scale_factor = np.sqrt(16.0 / memory_gb)  # 保持在16GB以内
            canvas_width = int(canvas_width * scale_factor)
            canvas_height = int(canvas_height * scale_factor)
            print(f"Applying scale factor {scale_factor:.3f} to reduce memory usage")
            print(f"Scaled canvas size: {canvas_width} x {canvas_height}")
        
        # 使用更小的数据类型来节省内存
        canvas = np.zeros((canvas_height, canvas_width, 3), dtype=np.float32)  # 改为float32
        weight_canvas = np.zeros((canvas_height, canvas_width), dtype=np.float32)
        
        # 逐个处理图像（避免并行时的内存爆炸）
        print("Processing images sequentially to save memory...")
        processed_count = 0
        
        # 使用tqdm显示进度
        with tqdm(total=len(positions), desc="Blending images", unit="img") as pbar:
            for (r, c), (x, y) in positions.items():
                if (r, c) not in self.images:
                    pbar.update(1)
                    continue
                
                img = self.images[(r, c)]
                h, w = img.shape[:2]
                
                # 计算缩放后的位置
                x_adj = (x + offset_x) * scale_factor
                y_adj = (y + offset_y) * scale_factor
                
                # 如果需要缩放，先缩放图像
                if scale_factor != 1.0:
                    new_w = int(w * scale_factor)
                    new_h = int(h * scale_factor)
                    img_scaled = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
                else:
                    img_scaled = img
                    new_w, new_h = w, h
                
                # 创建权重映射（基于缩放后的尺寸）
                weight_map = self.create_distance_weight_map((new_h, new_w))
                
                # 计算在画布上的位置范围
                x_start = int(np.floor(x_adj))
                y_start = int(np.floor(y_adj))
                x_end = min(x_start + new_w, canvas_width)
                y_end = min(y_start + new_h, canvas_height)
                
                # 确保不越界
                if x_start >= canvas_width or y_start >= canvas_height or x_end <= 0 or y_end <= 0:
                    pbar.update(1)
                    continue
                
                # 计算图像中对应的区域
                img_x_start = max(0, -x_start)
                img_y_start = max(0, -y_start)
                img_x_end = img_x_start + (x_end - max(0, x_start))
                img_y_end = img_y_start + (y_end - max(0, y_start))
                
                # 调整画布起始位置
                canvas_x_start = max(0, x_start)
                canvas_y_start = max(0, y_start)
                
                # 提取有效区域
                if img_x_end > img_x_start and img_y_end > img_y_start:
                    img_region = img_scaled[img_y_start:img_y_end, img_x_start:img_x_end]
                    weight_region = weight_map[img_y_start:img_y_end, img_x_start:img_x_end]
                    
                    # 累积到画布
                    canvas_region = canvas[canvas_y_start:canvas_y_start + img_region.shape[0], 
                                          canvas_x_start:canvas_x_start + img_region.shape[1]]
                    weight_canvas_region = weight_canvas[canvas_y_start:canvas_y_start + img_region.shape[0], 
                                                       canvas_x_start:canvas_x_start + img_region.shape[1]]
                    
                    # 更新画布（每个颜色通道）
                    for c_idx in range(3):
                        canvas[canvas_y_start:canvas_y_start + img_region.shape[0], 
                              canvas_x_start:canvas_x_start + img_region.shape[1], c_idx] += (
                            img_region[:, :, c_idx].astype(np.float32) * weight_region
                        )
                    
                    weight_canvas[canvas_y_start:canvas_y_start + img_region.shape[0], 
                                 canvas_x_start:canvas_x_start + img_region.shape[1]] += weight_region
                
                processed_count += 1
                if processed_count % 30 == 0 or processed_count == len(positions):
                    pbar.set_postfix({'processed': processed_count})
                pbar.update(1)
        
        # 归一化
        print("Normalizing final image...")
        mask = weight_canvas > 0
        result = np.zeros_like(canvas, dtype=np.uint8)
        
        for c_idx in range(3):
            result[:, :, c_idx][mask] = (
                canvas[:, :, c_idx][mask] / weight_canvas[mask]
            ).astype(np.uint8)
        
        # 保存结果
        cv2.imwrite(output_path, result)
        
        blend_time = time.time() - start_time
        print(f"✅ Memory-efficient blending completed in {blend_time:.2f} seconds")
        print(f"💾 Stitched image saved to: {output_path}")
        print(f"📏 Final image size: {canvas_width} x {canvas_height} x 3")
        if scale_factor != 1.0:
            print(f"📉 Note: Image was scaled by factor {scale_factor:.3f} to fit in memory")
        
        return result
    
    def blend_images_chunked(self, positions, output_path="stitched_result.jpg", chunk_size=4096):
        """分块处理大图像融合（适用于超大图像）- 修复形状不匹配问题"""
        print(f"\nChunked blending (chunk size: {chunk_size}x{chunk_size})...")
        start_time = time.time()
        
        # 计算画布尺寸
        canvas_width, canvas_height, offset_x, offset_y = self.calculate_canvas_size(positions)
        
        print(f"Full canvas size: {canvas_width} x {canvas_height}")
        
        # 计算分块数量
        num_chunks_x = (canvas_width + chunk_size - 1) // chunk_size
        num_chunks_y = (canvas_height + chunk_size - 1) // chunk_size
        
        print(f"Processing in {num_chunks_x} x {num_chunks_y} chunks...")
        
        # 创建最终结果图像（分块写入）
        result = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)
        
        for chunk_y in range(num_chunks_y):
            for chunk_x in range(num_chunks_x):
                print(f"  Processing chunk ({chunk_x+1}/{num_chunks_x}, {chunk_y+1}/{num_chunks_y})...")
                
                # 计算当前块的边界
                y_start = chunk_y * chunk_size
                y_end = min(y_start + chunk_size, canvas_height)
                x_start = chunk_x * chunk_size
                x_end = min(x_start + chunk_size, canvas_width)
                
                # 为当前块创建画布
                chunk_h = y_end - y_start
                chunk_w = x_end - x_start
                chunk_canvas = np.zeros((chunk_h, chunk_w, 3), dtype=np.float32)
                chunk_weight = np.zeros((chunk_h, chunk_w), dtype=np.float32)
                
                # 处理与当前块相交的图像
                for (r, c), (x, y) in positions.items():
                    if (r, c) not in self.images:
                        continue
                    
                    img = self.images[(r, c)]
                    img_h, img_w = img.shape[:2]
                    
                    # 图像在画布上的位置（使用整数位置避免浮点数问题）
                    img_x_start = int(np.round(x + offset_x))
                    img_y_start = int(np.round(y + offset_y))
                    img_x_end = img_x_start + img_w
                    img_y_end = img_y_start + img_h
                    
                    # 检查是否与当前块相交
                    if (img_x_end <= x_start or img_x_start >= x_end or 
                        img_y_end <= y_start or img_y_start >= y_end):
                        continue
                    
                    # 计算相交区域（确保边界一致）
                    overlap_x_start = max(img_x_start, x_start)
                    overlap_y_start = max(img_y_start, y_start)
                    overlap_x_end = min(img_x_end, x_end)
                    overlap_y_end = min(img_y_end, y_end)
                    
                    # 在图像中的位置
                    in_img_x_start = overlap_x_start - img_x_start
                    in_img_y_start = overlap_y_start - img_y_start
                    in_img_x_end = overlap_x_end - img_x_start
                    in_img_y_end = overlap_y_end - img_y_start
                    
                    # 在块中的位置
                    in_chunk_x_start = overlap_x_start - x_start
                    in_chunk_y_start = overlap_y_start - y_start
                    in_chunk_x_end = overlap_x_end - x_start
                    in_chunk_y_end = overlap_y_end - y_start
                    
                    # 确保索引有效
                    if (in_img_x_end <= in_img_x_start or in_img_y_end <= in_img_y_start or
                        in_chunk_x_end <= in_chunk_x_start or in_chunk_y_end <= in_chunk_y_start):
                        continue
                    
                    # 提取图像区域
                    img_region = img[in_img_y_start:in_img_y_end, in_img_x_start:in_img_x_end]
                    
                    # 创建权重（仅对相交区域）
                    weight_map_full = self.create_distance_weight_map(img.shape)
                    weight_region = weight_map_full[in_img_y_start:in_img_y_end, in_img_x_start:in_img_x_end]
                    
                    # 确保形状一致（防止边界误差）
                    expected_h = in_chunk_y_end - in_chunk_y_start
                    expected_w = in_chunk_x_end - in_chunk_x_start
                    
                    if img_region.shape[:2] != (expected_h, expected_w):
                        print(f"    Warning: Shape mismatch for image ({r},{c}), skipping...")
                        continue
                    
                    if weight_region.shape != (expected_h, expected_w):
                        print(f"    Warning: Weight shape mismatch for image ({r},{c}), skipping...")
                        continue
                    
                    # 累积到块画布
                    try:
                        for c_idx in range(3):
                            chunk_canvas[in_chunk_y_start:in_chunk_y_end, 
                                       in_chunk_x_start:in_chunk_x_end, c_idx] += (
                                img_region[:, :, c_idx].astype(np.float32) * weight_region
                            )
                        
                        chunk_weight[in_chunk_y_start:in_chunk_y_end, 
                                   in_chunk_x_start:in_chunk_x_end] += weight_region
                                
                    except Exception as e:
                        print(f"    Error processing image ({r},{c}): {e}")
                        print(f"    img_region shape: {img_region.shape}")
                        print(f"    weight_region shape: {weight_region.shape}")
                        print(f"    chunk region: [{in_chunk_y_start}:{in_chunk_y_end}, {in_chunk_x_start}:{in_chunk_x_end}]")
                        continue
                
                # 归一化当前块
                mask = chunk_weight > 0
                chunk_result = np.zeros_like(chunk_canvas, dtype=np.uint8)
                
                for c_idx in range(3):
                    chunk_result[:, :, c_idx][mask] = (
                        chunk_canvas[:, :, c_idx][mask] / chunk_weight[mask]
                    ).astype(np.uint8)
                
                # 写入最终结果
                result[y_start:y_end, x_start:x_end] = chunk_result
        
        # 保存结果
        cv2.imwrite(output_path, result)
        
        blend_time = time.time() - start_time
        print(f"\nChunked blending completed in {blend_time:.2f} seconds")
        print(f"Stitched image saved to: {output_path}")
        print(f"Final image size: {canvas_width} x {canvas_height} x 3")
        
        return result
    
    def blend_images_chunked_safe(self, positions, output_path="stitched_result.jpg", chunk_size=2048):
        """更安全的分块处理（使用更小的块和更严格的边界检查）"""
        print(f"\nSafe chunked blending (chunk size: {chunk_size}x{chunk_size})...")
        start_time = time.time()
        
        # 计算画布尺寸
        canvas_width, canvas_height, offset_x, offset_y = self.calculate_canvas_size(positions)
        
        print(f"Full canvas size: {canvas_width} x {canvas_height}")
        
        # 使用更小的块来减少内存压力
        num_chunks_x = (canvas_width + chunk_size - 1) // chunk_size
        num_chunks_y = (canvas_height + chunk_size - 1) // chunk_size
        
        print(f"Processing in {num_chunks_x} x {num_chunks_y} chunks...")
        
        # 分块保存到磁盘，避免创建巨大的结果数组
        temp_files = []
        
        for chunk_y in range(num_chunks_y):
            chunk_row_files = []
            for chunk_x in range(num_chunks_x):
                print(f"  Processing chunk ({chunk_x+1}/{num_chunks_x}, {chunk_y+1}/{num_chunks_y})...")
                
                # 计算当前块的边界
                y_start = chunk_y * chunk_size
                y_end = min(y_start + chunk_size, canvas_height)
                x_start = chunk_x * chunk_size
                x_end = min(x_start + chunk_size, canvas_width)
                
                # 为当前块创建画布
                chunk_h = y_end - y_start
                chunk_w = x_end - x_start
                chunk_canvas = np.zeros((chunk_h, chunk_w, 3), dtype=np.float32)
                chunk_weight = np.zeros((chunk_h, chunk_w), dtype=np.float32)
                
                # 处理与当前块相交的图像
                images_processed = 0
                for (r, c), (x, y) in positions.items():
                    if (r, c) not in self.images:
                        continue
                    
                    img = self.images[(r, c)]
                    img_h, img_w = img.shape[:2]
                    
                    # 使用整数位置计算
                    img_x = int(np.round(x + offset_x))
                    img_y = int(np.round(y + offset_y))
                    
                    # 检查图像是否与当前块重叠
                    if (img_x + img_w <= x_start or img_x >= x_end or 
                        img_y + img_h <= y_start or img_y >= y_end):
                        continue
                    
                    # 计算重叠区域在图像中的位置
                    crop_x_start = max(0, x_start - img_x)
                    crop_y_start = max(0, y_start - img_y)
                    crop_x_end = min(img_w, x_end - img_x)
                    crop_y_end = min(img_h, y_end - img_y)
                    
                    # 计算重叠区域在块中的位置
                    paste_x_start = max(0, img_x - x_start)
                    paste_y_start = max(0, img_y - y_start)
                    paste_x_end = paste_x_start + (crop_x_end - crop_x_start)
                    paste_y_end = paste_y_start + (crop_y_end - crop_y_start)
                    
                    # 检查边界有效性
                    if (crop_x_end <= crop_x_start or crop_y_end <= crop_y_start or
                        paste_x_end <= paste_x_start or paste_y_end <= paste_y_start):
                        continue
                    
                    # 提取图像区域
                    img_crop = img[crop_y_start:crop_y_end, crop_x_start:crop_x_end]
                    
                    # 创建对应的权重
                    weight_full = self.create_distance_weight_map(img.shape)
                    weight_crop = weight_full[crop_y_start:crop_y_end, crop_x_start:crop_x_end]
                    
                    # 确保尺寸匹配
                    expected_h = paste_y_end - paste_y_start
                    expected_w = paste_x_end - paste_x_start
                    
                    if img_crop.shape[:2] != (expected_h, expected_w):
                        continue
                    
                    # 添加到块画布
                    for c_idx in range(3):
                        chunk_canvas[paste_y_start:paste_y_end, paste_x_start:paste_x_end, c_idx] += (
                            img_crop[:, :, c_idx].astype(np.float32) * weight_crop
                        )
                    
                    chunk_weight[paste_y_start:paste_y_end, paste_x_start:paste_x_end] += weight_crop
                    images_processed += 1
                
                # 归一化并保存块
                mask = chunk_weight > 0
                chunk_result = np.zeros_like(chunk_canvas, dtype=np.uint8)
                
                for c_idx in range(3):
                    chunk_result[:, :, c_idx][mask] = (
                        chunk_canvas[:, :, c_idx][mask] / chunk_weight[mask]
                    ).astype(np.uint8)
                
                # 保存块到临时文件
                temp_filename = f"temp_chunk_{chunk_y}_{chunk_x}.png"
                cv2.imwrite(temp_filename, chunk_result)
                chunk_row_files.append(temp_filename)
                
                print(f"    Chunk completed with {images_processed} images")
            
            temp_files.append(chunk_row_files)
        
        # 合并所有块
        print("  Merging chunks...")
        result = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)
        
        for chunk_y, chunk_row_files in enumerate(temp_files):
            for chunk_x, temp_filename in enumerate(chunk_row_files):
                y_start = chunk_y * chunk_size
                y_end = min(y_start + chunk_size, canvas_height)
                x_start = chunk_x * chunk_size
                x_end = min(x_start + chunk_size, canvas_width)
                
                chunk_img = cv2.imread(temp_filename)
                if chunk_img is not None:
                    result[y_start:y_end, x_start:x_end] = chunk_img
                
                # 删除临时文件
                try:
                    os.remove(temp_filename)
                except:
                    pass
        
        # 保存最终结果
        cv2.imwrite(output_path, result)
        
        blend_time = time.time() - start_time
        print(f"\nSafe chunked blending completed in {blend_time:.2f} seconds")
        print(f"Stitched image saved to: {output_path}")
        print(f"Final image size: {canvas_width} x {canvas_height} x 3")
        
        return result
    
    # 更新智能融合方法
    def blend_images(self, positions, output_path="stitched_result.jpg"):
        """智能选择融合方法（针对32GB内存优化）"""
        canvas_width, canvas_height, _, _ = self.calculate_canvas_size(positions)
        memory_gb = (canvas_width * canvas_height * 3 * 8) / (1024**3)
        
        print(f"Memory analysis: {memory_gb:.2f}GB required, 32GB available")
        
        if memory_gb > 25.0:  # 如果需要超过25GB内存，使用最安全的方法
            print(f"Very large image detected ({memory_gb:.2f}GB), using safe chunked processing...")
            return self.blend_images_chunked_safe(positions, output_path)
        elif memory_gb > 15.0:  # 如果需要超过15GB内存
            print(f"Large image detected ({memory_gb:.2f}GB), using chunked processing...")
            return self.blend_images_chunked(positions, output_path)
        elif memory_gb > 8.0:  # 如果需要超过8GB内存
            print(f"Medium image detected ({memory_gb:.2f}GB), using memory-efficient processing...")
            return self.blend_images_memory_efficient(positions, output_path)
        else:
            print(f"Small image detected ({memory_gb:.2f}GB), using high-performance parallel processing...")
            return self.blend_images_parallel(positions, output_path)
    
    def calculate_canvas_size(self, positions):
        """计算拼接画布的总尺寸"""
        if not positions:
            return 0, 0, 0, 0
        
        # 获取第一张图片的尺寸作为参考
        sample_img = next(iter(self.images.values()))
        img_height, img_width = sample_img.shape[:2]  # 彩色图像是3维的
        
        # 计算所有图片的边界
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        for (r, c), (x, y) in positions.items():
            if (r, c) in self.images:
                # 图片的四个角
                corners = [
                    (x, y),  # 左上角
                    (x + img_width, y),  # 右上角
                    (x, y + img_height),  # 左下角
                    (x + img_width, y + img_height)  # 右下角
                ]
                
                for corner_x, corner_y in corners:
                    min_x = min(min_x, corner_x)
                    max_x = max(max_x, corner_x)
                    min_y = min(min_y, corner_y)
                    max_y = max(max_y, corner_y)
        
        # 计算画布尺寸（向上取整，但保留小数偏移）
        canvas_width = int(np.ceil(max_x - min_x))
        canvas_height = int(np.ceil(max_y - min_y))
        
        # 偏移量，用于将所有位置转换为正坐标（保持浮点精度）
        offset_x = -min_x
        offset_y = -min_y
        
        print(f"Canvas size: {canvas_width} x {canvas_height}")
        print(f"Offset: ({offset_x:.6f}, {offset_y:.6f})")
        
        return canvas_width, canvas_height, offset_x, offset_y
    
    def create_distance_weight_map(self, img_shape):
        """创建基于距离的权重映射，用于linear blending"""
        h, w = img_shape[:2]  # 支持彩色图像
        
        # 创建坐标网格
        y_coords, x_coords = np.mgrid[0:h, 0:w]
        
        # 计算到边界的最小距离
        dist_to_left = x_coords
        dist_to_right = w - 1 - x_coords
        dist_to_top = y_coords
        dist_to_bottom = h - 1 - y_coords
        
        # 权重 = 到最近边界的距离 + 1（避免零权重）
        weight_map = np.minimum(
            np.minimum(dist_to_left, dist_to_right),
            np.minimum(dist_to_top, dist_to_bottom)
        ) + 1
        
        return weight_map.astype(np.float32)
    
    def blend_images_parallel(self, positions, output_path="stitched_result.jpg"):
        """并行融合所有彩色图片（32GB内存优化版本）"""
        print(f"\nHigh-performance parallel blending with {self.num_threads} threads...")
        print("Utilizing 32GB memory for maximum performance...")
        start_time = time.time()
        
        # 计算画布尺寸
        canvas_width, canvas_height, offset_x, offset_y = self.calculate_canvas_size(positions)
        memory_gb = (canvas_width * canvas_height * 3 * 8) / (1024**3)
        
        # 针对32GB内存优化：使用更高精度的数据类型
        print(f"Creating high-precision canvas: {canvas_width} x {canvas_height}")
        canvas = np.zeros((canvas_height, canvas_width, 3), dtype=np.float64)  # 保持float64精度
        weight_canvas = np.zeros((canvas_height, canvas_width), dtype=np.float64)
        
        def process_single_image(item):
            """处理单张图片的变换和权重计算"""
            (r, c), (x, y) = item
            
            if (r, c) not in self.images:
                return None
            
            img = self.images[(r, c)]
            h, w = img.shape[:2]
            
            # 计算亚像素精度的调整位置
            x_adj = x + offset_x
            y_adj = y + offset_y
            
            # 使用仿射变换进行亚像素精度的图像变换
            M = np.float32([[1, 0, x_adj], [0, 1, y_adj]])
            
            # 变换图像到画布坐标系
            transformed_img = cv2.warpAffine(
                img, M, (canvas_width, canvas_height), 
                flags=cv2.INTER_LINEAR,  # 高质量插值
                borderMode=cv2.BORDER_CONSTANT,
                borderValue=0
            )
            
            # 创建权重映射
            weight_map = self.create_distance_weight_map(img.shape)
            
            # 同样变换权重映射
            transformed_weight = cv2.warpAffine(
                weight_map, M, (canvas_width, canvas_height),
                flags=cv2.INTER_LINEAR,
                borderMode=cv2.BORDER_CONSTANT,
                borderValue=0
            )
            
            return {
                'position': (r, c),
                'transformed_img': transformed_img,
                'transformed_weight': transformed_weight,
                'coords': (x_adj, y_adj)
            }
        
        # 并行处理所有图片的变换
        print("Transforming all images in parallel...")
        transform_results = []
        
        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            future_to_pos = {executor.submit(process_single_image, item): item for item in positions.items()}
            
            completed = 0
            with tqdm(total=len(positions), desc="Transforming", unit="img") as pbar:
                for future in as_completed(future_to_pos):
                    result = future.result()
                    if result:
                        transform_results.append(result)
                    completed += 1
                    if completed % 30 == 0 or completed == len(positions):
                        pbar.set_postfix({'transformed': len(transform_results)})
                    pbar.update(1)
        
        # 串行累积到画布（这部分需要同步，避免竞争条件）
        print("Accumulating to high-precision canvas...")
        with tqdm(total=len(transform_results), desc="Accumulating", unit="img") as pbar:
            for i, result in enumerate(transform_results):
                transformed_img = result['transformed_img']
                transformed_weight = result['transformed_weight']
                r, c = result['position']
                x_adj, y_adj = result['coords']
                
                # 创建有效区域掩码
                mask = transformed_weight > 0
                
                # 累积到画布（每个颜色通道分别处理）- 修复广播错误
                for c_idx in range(3):
                    # 正确的方式：对图像和权重都应用相同的mask，确保形状匹配
                    canvas[:, :, c_idx][mask] += (
                        transformed_img[:, :, c_idx][mask].astype(np.float64) * 
                        transformed_weight[mask]
                    )
                weight_canvas[mask] += transformed_weight[mask]
                
                if (i + 1) % 30 == 0 or (i + 1) == len(transform_results):
                    pbar.set_postfix({'accumulated': i + 1})
                pbar.update(1)
        
        # 归一化
        print("Normalizing with high precision...")
        mask = weight_canvas > 0
        result = np.zeros_like(canvas, dtype=np.uint8)
        
        for c_idx in range(3):
            result[:, :, c_idx][mask] = (
                canvas[:, :, c_idx][mask] / weight_canvas[mask]
            ).astype(np.uint8)
        
        # 保存结果
        print(f"Saving final result...")
        cv2.imwrite(output_path, result)
        
        blend_time = time.time() - start_time
        print(f"✅ High-performance parallel blending completed in {blend_time:.2f} seconds")
        print(f"💾 Stitched color image saved to: {output_path}")
        print(f"📏 Final image size: {canvas_width} x {canvas_height} x 3")
        print(f"💾 Memory utilization: ~{memory_gb:.1f}GB of 32GB available")
        
        return result
    
    def global_optimization(self, pairwise_offsets):
        """使用全局优化计算位置，减少累积误差 - (0,0)图片固定为原点"""
        # 创建变量索引 - 排除(0,0)图片
        tile_to_idx = {}
        idx_to_tile = {}
        idx = 0
        for r in range(self.rows):
            for c in range(self.cols):
                if (r, c) in self.images and (r, c) != (0, 0):  # 排除(0,0)
                    tile_to_idx[(r, c)] = idx
                    idx_to_tile[idx] = (r, c)
                    idx += 1
        
        num_tiles = len(tile_to_idx)  # 不包括(0,0)
        print(f"Global optimization: {num_tiles} tiles (excluding fixed reference (0,0))")
        
        # 准备约束数据
        constraints = []
        weights = []
        
        for pair, (dx, dy, confidence) in pairwise_offsets.items():
            tile1, tile2 = pair
            
            # 处理涉及(0,0)的约束
            if tile1 == (0, 0) and tile2 in tile_to_idx:
                # (0,0) -> tile2: tile2的位置 = (0,0) + offset = offset
                idx2 = tile_to_idx[tile2]
                constraints.append(('fixed_to_var', idx2, dx, dy))
                weights.append(confidence)
            elif tile2 == (0, 0) and tile1 in tile_to_idx:
                # tile1 -> (0,0): (0,0)的位置 = tile1 + offset，即 tile1 = -offset
                idx1 = tile_to_idx[tile1]
                constraints.append(('var_to_fixed', idx1, dx, dy))
                weights.append(confidence)
            elif tile1 in tile_to_idx and tile2 in tile_to_idx:
                # 两个都是变量
                idx1 = tile_to_idx[tile1]
                idx2 = tile_to_idx[tile2]
                constraints.append(('var_to_var', idx1, idx2, dx, dy))
                weights.append(confidence)
        
        # 初始位置估计（简单累加结果）- 基于(0,0)为原点
        initial_positions = {(0, 0): (0, 0)}
        
        # 第0行从左到右
        for c in range(1, self.cols):
            if ((0, c-1), (0, c)) in pairwise_offsets:
                prev_x, prev_y = initial_positions[(0, c-1)]
                dx, dy, _ = pairwise_offsets[((0, c-1), (0, c))]
                initial_positions[(0, c)] = (prev_x + dx, prev_y + dy)
        
        # 其他行
        for r in range(1, self.rows):
            for c in range(self.cols):
                if ((r-1, c), (r, c)) in pairwise_offsets and (r-1, c) in initial_positions:
                    prev_x, prev_y = initial_positions[(r-1, c)]
                    dx, dy, _ = pairwise_offsets[((r-1, c), (r, c))]
                    initial_positions[(r, c)] = (prev_x + dx, prev_y + dy)
        
        # 构建初始参数向量（只包含非(0,0)的图片）
        x0 = np.zeros(2 * num_tiles)
        for tile, (x, y) in initial_positions.items():
            if tile in tile_to_idx:
                idx = tile_to_idx[tile]
                x0[2*idx] = x
                x0[2*idx + 1] = y
        
        # 定义残差函数
        def residuals(params):
            residual_list = []
            for i, constraint in enumerate(constraints):
                weight = weights[i]
                
                if constraint[0] == 'fixed_to_var':
                    # (0,0) -> tile: tile的位置应该等于offset
                    _, idx2, dx_obs, dy_obs = constraint
                    x2, y2 = params[2*idx2], params[2*idx2 + 1]
                    # (0,0)的位置是(0,0)，所以 tile2 - (0,0) = (x2, y2)
                    dx_pred, dy_pred = x2, y2
                    
                elif constraint[0] == 'var_to_fixed':
                    # tile -> (0,0): tile的位置 + offset = (0,0)
                    _, idx1, dx_obs, dy_obs = constraint
                    x1, y1 = params[2*idx1], params[2*idx1 + 1]
                    # tile1 + offset = (0,0)，所以 offset = (0,0) - tile1 = (-x1, -y1)
                    dx_pred, dy_pred = -x1, -y1
                    
                elif constraint[0] == 'var_to_var':
                    # tile1 -> tile2: 正常的变量间约束
                    _, idx1, idx2, dx_obs, dy_obs = constraint
                    x1, y1 = params[2*idx1], params[2*idx1 + 1]
                    x2, y2 = params[2*idx2], params[2*idx2 + 1]
                    dx_pred = x2 - x1
                    dy_pred = y2 - y1
                
                residual_list.append(weight * (dx_pred - dx_obs))
                residual_list.append(weight * (dy_pred - dy_obs))
            
            return np.array(residual_list)
        
        # 执行优化
        print("Performing global optimization with fixed reference at (0,0)...")
        result = least_squares(residuals, x0, method='lm')
        
        if result.success:
            print("Global optimization converged successfully")
        else:
            print("Global optimization failed, using initial solution")
            result.x = x0
        
        # 提取优化后的位置
        optimized_positions = {(0, 0): (0.0, 0.0)}  # 固定(0,0)为原点
        
        for idx in range(num_tiles):
            tile = idx_to_tile[idx]
            x_opt = result.x[2*idx]
            y_opt = result.x[2*idx + 1]
            optimized_positions[tile] = (x_opt, y_opt)
        
        return optimized_positions
    
    def calculate_global_positions(self, pairwise_offsets):
        """计算全局绝对位置 - 使用全局优化"""
        return self.global_optimization(pairwise_offsets)
    
    def load_imagej_results(self, filepath):
        """加载ImageJ的配准结果"""
        imagej_positions = {}
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('r') and '.jpg' in line:
                        parts = line.split(';')
                        filename = parts[0].strip()
                        coords_str = parts[2].strip()
                        # 解析坐标 "(x, y)"
                        coords_str = coords_str.strip('()')
                        x, y = map(float, coords_str.split(','))
                        
                        # 从文件名提取行列信息
                        basename = filename.replace('.jpg', '')
                        r = int(basename.split('_')[0][1:])
                        c = int(basename.split('_')[1][1:])
                        
                        imagej_positions[(r, c)] = (x, y)
            print(f"Loaded ImageJ results: {len(imagej_positions)} positions")
        return imagej_positions
    
    def compare_with_imagej(self, our_positions, imagej_positions):
        """比较我们的结果与ImageJ的结果"""
        print("\n" + "="*80)
        print("COMPARISON WITH IMAGEJ RESULTS (Subpixel Precision)")
        print("="*80)
        print(f"{'Position':<12} {'Our Result':<25} {'ImageJ Result':<25} {'Difference':<15}")
        print("-" * 80)
        
        total_diff = 0
        count = 0
        
        for (r, c) in sorted(our_positions.keys()):
            if (r, c) in imagej_positions:
                our_x, our_y = our_positions[(r, c)]
                ij_x, ij_y = imagej_positions[(r, c)]
                
                diff_x = our_x - ij_x
                diff_y = our_y - ij_y
                diff_magnitude = np.sqrt(diff_x**2 + diff_y**2)
                
                print(f"({r},{c}):      ({our_x:10.6f}, {our_y:10.6f})   ({ij_x:10.4f}, {ij_y:10.4f})   {diff_magnitude:8.6f}")
                
                total_diff += diff_magnitude
                count += 1
        
        if count > 0:
            avg_diff = total_diff / count
            print("-" * 80)
            print(f"Average difference: {avg_diff:.6f} pixels")
    
    def save_configuration(self, positions, filename="TileConfiguration_opencv_subpixel.txt"):
        """保存配置文件"""
        with open(filename, 'w') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n\n")
            f.write("# Define the image coordinates (subpixel precision)\n")
            
            for (r, c), (x, y) in positions.items():
                if (r, c) in self.images:
                    img_name = f"r{r:03d}_c{c:03d}.jpg"
                    f.write(f"{img_name}; ; ({x:.6f}, {y:.6f})\n")
        
        print(f"Configuration saved to {filename}")
    
    def run_stitching(self, create_result_image=True, output_path=None):
        """执行完整的拼接流程（32GB内存优化版本）"""
        total_start = time.time()
        
        print("🚀 " + "="*70)
        print("🚀 HIGH-PERFORMANCE IMAGE STITCHING WITH SUBPIXEL PRECISION")
        print("🚀 " + "="*70)
        print(f"🧵 Threads: {self.num_threads}")
        print(f"💾 Memory: 32GB optimized")
        print(f"🎯 Grid: {self.rows}×{self.cols} = {self.rows * self.cols} images")
        print(f"📁 Directory: {self.image_dir}")
        print("🚀 " + "="*70)
        
        print("\n📋 Step 1: Loading color images...")
        step1_start = time.time()
        self.load_images()  # 调用并行版本
        step1_time = time.time() - step1_start
        
        if not self.images:
            print("❌ No images loaded!")
            return
        
        print(f"✅ Step 1 completed in {step1_time:.2f}s - Loaded {len(self.images)} color images")
        
        print("\n🔍 Step 2: Calculating pairwise offsets...")
        step2_start = time.time()
        pairwise_offsets = self.calculate_pairwise_offsets()  # 调用并行版本
        step2_time = time.time() - step2_start
        print(f"✅ Step 2 completed in {step2_time:.2f}s - Calculated {len(pairwise_offsets)} offsets")
        
        print("\n🎯 Step 3: Global optimization...")
        step3_start = time.time()
        positions = self.calculate_global_positions(pairwise_offsets)
        step3_time = time.time() - step3_start
        print(f"✅ Step 3 completed in {step3_time:.2f}s - Optimized {len(positions)} positions")
        
        print("\n📍 Step 4: Final optimized positions (subpixel precision):")
        for (r, c), (x, y) in sorted(positions.items()):
            print(f"  Image ({r},{c}): position ({x:.6f}, {y:.6f})")
        
        # 保存配置文件
        self.save_configuration(positions)
        
        # 加载并比较ImageJ结果
        imagej_file = os.path.join(self.image_dir, "TileConfiguration.registered-imagej.txt")
        imagej_positions = self.load_imagej_results(imagej_file)
        if imagej_positions:
            self.compare_with_imagej(positions, imagej_positions)
        
        # 创建拼接结果图像
        if create_result_image:
            if output_path is None:
                output_path = f"{self.image_dir}_stitched_32gb_opencv.jpg"
            
            print("\n🎨 Step 5: Creating stitched image with 32GB memory optimization...")
            step5_start = time.time()
            result_image = self.blend_images(positions, output_path)  # 调用优化版本
            step5_time = time.time() - step5_start
            print(f"✅ Step 5 completed in {step5_time:.2f}s")
            
            # 显示总时间和各步骤时间
            total_time = time.time() - total_start
            print("\n🏁 " + "="*70)
            print("🏁 STITCHING COMPLETED SUCCESSFULLY!")
            print("🏁 " + "="*70)
            print(f"⏱️  Step 1 (Loading):      {step1_time:8.2f}s")
            print(f"⏱️  Step 2 (Phase Corr):   {step2_time:8.2f}s") 
            print(f"⏱️  Step 3 (Optimization): {step3_time:8.2f}s")
            print(f"⏱️  Step 5 (Blending):     {step5_time:8.2f}s")
            print(f"⏱️  TOTAL TIME:           {total_time:8.2f}s")
            print("🏁 " + "="*70)
            print(f"💻 Optimized for 32GB memory system")
            print(f"⚡ Using {self.num_threads} threads for maximum performance")
            print(f"📸 Final result: {output_path}")
            
            return positions, result_image
        
        total_time = time.time() - total_start
        print(f"\n🎉 High-performance stitching completed in {total_time:.2f} seconds!")
        
        return positions

def main():
    # 设置参数
    image_dir = "test02"  # 测试数据集
    rows = 6
    cols = 16
    overlap_ratio = 0.1
    num_threads = 20  # 使用16线程
    
    # 创建并行拼接器
    stitcher = CVStitcher(image_dir, rows, cols, overlap_ratio, num_threads)
    
    # 执行并行拼接
    result = stitcher.run_stitching(create_result_image=True)
    
    if result:
        positions, stitched_image = result
        print(f"\n✅ High-performance subpixel precision color stitching completed!")
        print(f"📊 Final stitched image shape: {stitched_image.shape}")
        print(f"🚀 Using {num_threads} threads for maximum performance")
    else:
        print("\n❌ Stitching failed!")

if __name__ == "__main__":
    main()
