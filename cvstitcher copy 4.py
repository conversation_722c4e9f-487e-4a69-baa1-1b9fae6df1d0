import cv2
import numpy as np
import os
from scipy.optimize import least_squares
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from tqdm import tqdm
import torch
import torch.nn.functional as F
import multiprocessing
import re

class GridStitcher:
    """
    高性能图像拼接器 - 基于相位相关和全局优化
    
    核心技术：
    1. 相位相关算法进行亚像素精度配准
    2. 全局优化消除累积误差
    3. Linear blending实现无缝融合
    4. 多线程并行处理提升性能
    """
    
    def __init__(self, image_dir, rows=3, cols=3, overlap_ratio=0.1, num_threads=16, tile_config_path=None):
        """
        初始化拼接器
        
        Args:
            image_dir: 图像文件夹路径
            rows: 网格行数
            cols: 网格列数  
            overlap_ratio: 预估重叠度
            num_threads: 并行线程数
            tile_config_path: TileConfiguration.txt文件路径（可选）
        """
        self.image_dir = image_dir
        self.num_threads = num_threads
        
        # 如果提供了TileConfiguration.txt文件，解析获取网格信息
        if tile_config_path:
            grid_info = self.parse_tile_configuration(tile_config_path)
            self.rows = grid_info['rows']
            self.cols = grid_info['cols']
            self.overlap_ratio = grid_info['overlap_ratio']
            self.image_naming_format = grid_info['naming_format']
            self.initial_positions = grid_info['positions']
            print(f"📋 从配置文件解析得到:")
            print(f"   🎯 网格: {self.rows}×{self.cols} = {self.rows*self.cols} 张图像")
            print(f"   📐 重叠度: {self.overlap_ratio*100:.1f}%")
            print(f"   📝 命名格式: {self.image_naming_format}")
        else:
            self.rows = rows
            self.cols = cols
            self.overlap_ratio = overlap_ratio
            self.image_naming_format = "r_c"  # 默认格式 r000_c000.jpg
            self.initial_positions = None
        
        # 数据存储
        self.color_images = {}      # 彩色图像（用于最终融合）
        self.gray_images = {}       # 灰度图像（用于相位相关计算）
        self.pairwise_offsets = {}  # 相邻图像偏移量
        self.final_positions = {}   # 全局优化后的位置
        
        # GPU融合配置 - 针对RTX 3050 4GB优化
        self.use_gpu = True
        self.gpu_memory_limit_gb = 3.8
        self.gpu_chunk_size = 8192        # 介于8192和12000之间
        self.memory_limit_gb = 30
        self.max_feather_pixels = 50
        self.force_gpu_chunked = True   # 强制使用GPU分块模式
        
        print(f"🚀 初始化高性能图像拼接器")
        print(f"   📁 目录: {image_dir}")
        print(f"   🎯 网格: {self.rows}×{self.cols} = {self.rows*self.cols} 张图像")
        print(f"   🧵 线程: {num_threads}")
        print(f"   💾 内存: 32GB 优化")

    # ========================= TileConfiguration文件解析 =========================
    
    def parse_tile_configuration(self, config_path):
        """
        解析TileConfiguration.txt文件
        
        Args:
            config_path: TileConfiguration.txt文件路径
            
        Returns:
            dict: 包含网格信息的字典
        """
        print(f"\n📋 解析TileConfiguration文件: {config_path}")
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        positions = {}
        
        with open(config_path, 'r') as f:
            lines = f.readlines()
        
        # 解析图像坐标
        for line in lines:
            line = line.strip()
            if line.startswith('#') or line.startswith('dim') or not line:
                continue
            
            # 解析格式: filename ; ; (x, y)
            match = re.match(r'(\S+)\s*;\s*;\s*\(([-\d.]+),\s*([-\d.]+)\)', line)
            if match:
                filename = match.group(1)
                x = float(match.group(2))
                y = float(match.group(3))
                positions[filename] = (x, y)
        
        if not positions:
            raise ValueError("未能从配置文件中解析出任何图像坐标")
        
        print(f"   ✅ 解析完成，找到 {len(positions)} 个图像坐标")
        
        # 推断网格信息
        grid_info = self.calculate_grid_from_coordinates(positions)
        
        return grid_info
    
    def calculate_grid_from_coordinates(self, positions):
        """
        从图像坐标计算网格信息
        
        Args:
            positions: {filename: (x, y)} 字典
            
        Returns:
            dict: 网格信息
        """
        print(f"   🔍 分析图像坐标分布...")
        
        # 提取所有坐标
        coords = list(positions.values())
        x_coords = [coord[0] for coord in coords]
        y_coords = [coord[1] for coord in coords]
        
        # 获取唯一的X和Y坐标
        unique_x = sorted(list(set(x_coords)))
        unique_y = sorted(list(set(y_coords)))
        
        rows = len(unique_y)
        cols = len(unique_x)
        
        print(f"   📊 检测到 {rows} 行, {cols} 列")
        
        # 计算重叠度
        if len(unique_x) > 1:
            x_spacing = unique_x[1] - unique_x[0]
            # 假设图像尺寸为2448像素（根据示例数据）
            estimated_img_width = 2448
            overlap_ratio_x = max(0, (estimated_img_width - x_spacing) / estimated_img_width)
        else:
            overlap_ratio_x = 0.1
        
        if len(unique_y) > 1:
            y_spacing = unique_y[1] - unique_y[0]
            # 假设图像尺寸为2048像素
            estimated_img_height = 2048
            overlap_ratio_y = max(0, (estimated_img_height - y_spacing) / estimated_img_height)
        else:
            overlap_ratio_y = 0.1
        
        # 使用X方向重叠度作为主要重叠度
        overlap_ratio = overlap_ratio_x
        
        print(f"   📐 计算重叠度: X方向 {overlap_ratio_x*100:.1f}%, Y方向 {overlap_ratio_y*100:.1f}%")
        
        # 检测图像命名格式
        sample_filename = list(positions.keys())[0]
        if sample_filename.startswith('s_'):
            naming_format = "s_sequential"
        elif 'r' in sample_filename and 'c' in sample_filename:
            naming_format = "r_c"
        else:
            naming_format = "unknown"
        
        print(f"   📝 检测命名格式: {naming_format}")
        
        # 创建坐标映射（用于后续处理）
        coordinate_map = {}
        if naming_format == "s_sequential":
            # 对于s_0001.jpg格式，需要根据坐标推断网格位置
            coordinate_map = self._map_sequential_to_grid(positions, rows, cols)
        else:
            # 对于r000_c000.jpg格式，直接从文件名解析
            coordinate_map = self._map_rc_format(positions)
        
        return {
            'rows': rows,
            'cols': cols,
            'overlap_ratio': overlap_ratio,
            'naming_format': naming_format,
            'positions': positions,
            'coordinate_map': coordinate_map
        }
    
    def _map_sequential_to_grid(self, positions, rows, cols):
        """
        将顺序命名的图像映射到网格坐标
        
        策略：根据ImageJ的蛇形扫描模式进行映射
        - 第1行: 从左到右 (s_0001 到 s_0020)
        - 第2行: 从右到左 (s_0021 到 s_0040)
        - 第3行: 从左到右 (s_0041 到 s_0060)
        - 依此类推...
        """
        coordinate_map = {}
        
        # 按文件名排序
        sorted_files = sorted(positions.keys(), key=lambda x: int(x.split('_')[1].split('.')[0]))
        
        for idx, filename in enumerate(sorted_files):
            row = idx // cols
            if row % 2 == 0:  # 偶数行：从左到右
                col = idx % cols
            else:  # 奇数行：从右到左
                col = cols - 1 - (idx % cols)
            
            coordinate_map[filename] = (row, col)
        
        return coordinate_map
    
    def _map_rc_format(self, positions):
        """解析r000_c000.jpg格式的文件名"""
        coordinate_map = {}
        
        for filename in positions.keys():
            # 解析r000_c000.jpg格式
            match = re.match(r'r(\d+)_c(\d+)', filename)
            if match:
                row = int(match.group(1))
                col = int(match.group(2))
                coordinate_map[filename] = (row, col)
        
        return coordinate_map

    # ========================= 第一阶段：图像加载 =========================
    
    def load_images_parallel(self):
        """
        第一阶段：并行加载所有图像
        
        技术要点：
        - 同时加载彩色和灰度版本
        - 彩色图用于最终融合，灰度图用于相位相关
        - 多线程并行加载提升IO性能
        - 支持多种图像命名格式
        """
        print(f"\n📋 第一阶段：并行加载图像")
        print(f"   🧵 使用 {self.num_threads} 个线程并行加载")
        print(f"   📝 图像命名格式: {self.image_naming_format}")
        
        # 预处理：为s_sequential格式构建映射表
        grid_to_filename = {}
        if self.image_naming_format == "s_sequential" and hasattr(self, 'initial_positions'):
            config_path = os.path.join(self.image_dir, "TileConfiguration.txt")
            if os.path.exists(config_path):
                grid_info = self.parse_tile_configuration(config_path)
                coordinate_map = grid_info['coordinate_map']
                for filename, (grid_r, grid_c) in coordinate_map.items():
                    grid_to_filename[(grid_r, grid_c)] = filename
        
        def load_single_image(r, c):
            """加载单张图像的工作函数"""
            if self.image_naming_format == "s_sequential":
                # 对于s_0001.jpg格式，使用预构建的映射表
                if (r, c) in grid_to_filename:
                    filename = grid_to_filename[(r, c)]
                else:
                    return (r, c), None, None, False
            else:
                # 默认r000_c000.jpg格式
                filename = f"r{r:03d}_c{c:03d}.jpg"
            
            filepath = os.path.join(self.image_dir, filename)
            
            if os.path.exists(filepath):
                # 加载彩色图像
                color_img = cv2.imread(filepath, cv2.IMREAD_COLOR)
                if color_img is not None:
                    # 创建灰度版本用于相位相关
                    gray_img = cv2.cvtColor(color_img, cv2.COLOR_BGR2GRAY)
                    return (r, c), color_img, gray_img, True
            
            return (r, c), None, None, False
        
        start_time = time.time()
        tasks = [(r, c) for r in range(self.rows) for c in range(self.cols)]
        
        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            future_to_pos = {executor.submit(load_single_image, r, c): (r, c) for r, c in tasks}
            
            with tqdm(total=len(tasks), desc="🖼️  加载图像", unit="张") as pbar:
                for future in as_completed(future_to_pos):
                    (r, c), color_img, gray_img, success = future.result()
                    
                    if success:
                        self.color_images[(r, c)] = color_img
                        self.gray_images[(r, c)] = gray_img
                    
                    pbar.set_postfix({'已加载': len(self.color_images)})
                    pbar.update(1)
        
        load_time = time.time() - start_time
        print(f"   ✅ 加载完成：{len(self.color_images)} 张图像，耗时 {load_time:.2f}s")
        
        if not self.color_images:
            raise ValueError("❌ 未找到任何图像文件！")

    # ========================= 第二阶段：相位相关配准 =========================
    
    def calculate_phase_correlation(self, img1, img2):
        """
        核心算法：增强版相位相关
        
        原理：
        1. 将图像转换到频域进行匹配
        2. 利用相位信息计算精确偏移
        3. 亚像素精度的峰值检测
        4. 汉宁窗预处理减少频谱泄漏
        
        优势：
        - 对光照变化不敏感
        - 亚像素精度
        - 计算效率高
        - 减少频谱泄漏提高配准精度
        """
        def apply_hanning_window(img):
            """应用汉宁窗减少边界效应"""
            h, w = img.shape
            hann_h = np.hanning(h).reshape(-1, 1)
            hann_w = np.hanning(w).reshape(1, -1)
            hann_2d = hann_h * hann_w
            return img.astype(np.float32) * hann_2d
        
        def subpixel_peak_refinement(correlation_map, method='parabolic'):
            """亚像素峰值精化 - 支持多种精化方法"""
            peak_y, peak_x = np.unravel_index(np.argmax(correlation_map), correlation_map.shape)
            h, w = correlation_map.shape
            
            # 边界检查
            if peak_x == 0 or peak_x == w-1 or peak_y == 0 or peak_y == h-1:
                return peak_x, peak_y, correlation_map[peak_y, peak_x]
            
            if method == 'parabolic':
                # X方向抛物线拟合
                c1 = correlation_map[peak_y, peak_x-1]
                c2 = correlation_map[peak_y, peak_x]
                c3 = correlation_map[peak_y, peak_x+1]
                denom = (c1 + c3 - 2*c2)
                dx = 0.5 * (c1 - c3) / denom if abs(denom) > 1e-10 else 0
                
                # Y方向抛物线拟合
                c1 = correlation_map[peak_y-1, peak_x]
                c2 = correlation_map[peak_y, peak_x]
                c3 = correlation_map[peak_y+1, peak_x]
                denom = (c1 + c3 - 2*c2)
                dy = 0.5 * (c1 - c3) / denom if abs(denom) > 1e-10 else 0
            
            elif method == 'centroid':
                # 质心法，更稳定但可能不如抛物线拟合精确
                window_size = 3
                y_min = max(0, peak_y - window_size)
                y_max = min(h, peak_y + window_size + 1)
                x_min = max(0, peak_x - window_size)
                x_max = min(w, peak_x + window_size + 1)
                
                window = correlation_map[y_min:y_max, x_min:x_max].copy()
                window_sum = window.sum()
                if window_sum <= 1e-10:
                    return peak_x, peak_y, correlation_map[peak_y, peak_x]
                
                y_indices, x_indices = np.mgrid[y_min:y_max, x_min:x_max]
                dx = (window * (x_indices - peak_x)).sum() / window_sum
                dy = (window * (y_indices - peak_y)).sum() / window_sum
            
            else:
                return peak_x, peak_y, correlation_map[peak_y, peak_x]
                
            # 计算峰值处的幅值（用于置信度计算）
            # 使用双线性插值估计亚像素位置处的相关值
            if 0 <= peak_x + dx < w-1 and 0 <= peak_y + dy < h-1:
                x0, y0 = int(peak_x + dx), int(peak_y + dy)
                alpha_x, alpha_y = peak_x + dx - x0, peak_y + dy - y0
                
                # 双线性插值
                v1 = correlation_map[y0, x0] * (1-alpha_x) + correlation_map[y0, x0+1] * alpha_x
                v2 = correlation_map[y0+1, x0] * (1-alpha_x) + correlation_map[y0+1, x0+1] * alpha_x
                peak_value = v1 * (1-alpha_y) + v2 * alpha_y
            else:
                peak_value = correlation_map[peak_y, peak_x]
            
            return peak_x + dx, peak_y + dy, peak_value
        
        # 输入尺寸检查和调整
        if img1.shape != img2.shape:
            h = min(img1.shape[0], img2.shape[0])
            w = min(img1.shape[1], img2.shape[1])
            img1_crop = img1[:h, :w]
            img2_crop = img2[:h, :w]
        else:
            img1_crop, img2_crop = img1, img2
        
        # 优化FFT计算的尺寸（快速傅里叶变换在2的幂次上最高效）
        optimal_h = 2 ** int(np.ceil(np.log2(img1_crop.shape[0])))
        optimal_w = 2 ** int(np.ceil(np.log2(img1_crop.shape[1])))
        
        # 应用汉宁窗减少频谱泄漏
        img1_windowed = apply_hanning_window(img1_crop)
        img2_windowed = apply_hanning_window(img2_crop)
        
        # 零填充到最佳FFT尺寸
        img1_padded = np.zeros((optimal_h, optimal_w), dtype=np.float32)
        img2_padded = np.zeros((optimal_h, optimal_w), dtype=np.float32)
        img1_padded[:img1_crop.shape[0], :img1_crop.shape[1]] = img1_windowed
        img2_padded[:img2_crop.shape[0], :img2_crop.shape[1]] = img2_windowed
        
        # 相位相关核心计算
        f1 = np.fft.fft2(img1_padded)
        f2 = np.fft.fft2(img2_padded)
        
        # 计算归一化的互功率谱
        cross_power = f1 * np.conj(f2)
        cross_power_abs = np.abs(cross_power) + 1e-15  # 防止除零
        cross_power_norm = cross_power / cross_power_abs
        
        # 执行逆变换得到相关结果
        correlation = np.real(np.fft.ifft2(cross_power_norm))
        correlation = np.fft.fftshift(correlation)  # 将零频率分量移到中心
        
        # 亚像素峰值检测
        center_h, center_w = optimal_h // 2, optimal_w // 2
        search_window = 20  # 搜索窗口大小，可以调整
        
        # 在中心区域搜索峰值
        y_min = max(0, center_h - search_window)
        y_max = min(optimal_h, center_h + search_window)
        x_min = max(0, center_w - search_window)
        x_max = min(optimal_w, center_w + search_window)
        
        search_region = correlation[y_min:y_max, x_min:x_max]
        local_peak_y, local_peak_x = np.unravel_index(np.argmax(search_region), search_region.shape)
        peak_x, peak_y = local_peak_x + x_min, local_peak_y + y_min
        
        # 亚像素精化
        peak_x_sub, peak_y_sub, peak_value = subpixel_peak_refinement(
            correlation, method='parabolic'
        )
        
        # 转换到图像坐标系（相对于中心）
        dx = peak_x_sub - center_w
        dy = peak_y_sub - center_h
        
        # 计算置信度指标
        max_val = peak_value
        # 计算峰值外的平均值和标准差，用于SNR
        mask = np.ones_like(correlation, dtype=bool)
        win_size = 5  # 排除峰值周围的窗口大小
        y_min = max(0, int(peak_y_sub) - win_size)
        y_max = min(correlation.shape[0], int(peak_y_sub) + win_size + 1)
        x_min = max(0, int(peak_x_sub) - win_size)
        x_max = min(correlation.shape[1], int(peak_x_sub) + win_size + 1)
        mask[y_min:y_max, x_min:x_max] = False
        
        bg_mean = correlation[mask].mean()
        bg_std = correlation[mask].std()
        
        # 信噪比作为置信度
        snr = (max_val - bg_mean) / (bg_std + 1e-10)
        confidence = min(1.0, snr / 20.0)  # 归一化到0-1范围
        
        return dx, dy, confidence
    
    def extract_overlap_roi(self, img1, img2, direction):
        """
        智能提取重叠区域
        
        策略：
        - 根据ImageJ日志数据估计实际重叠度
        - 动态调整ROI尺寸
        - 确保有足够的特征用于匹配
        """
        h1, w1 = img1.shape
        
        if direction == 'horizontal':
            # 水平重叠：使用右侧和左侧区域
            estimated_overlap = (2448 - 2181) / 2448  # 基于ImageJ数据
            overlap_width = max(int(w1 * estimated_overlap), 200)
            roi1 = img1[:, -overlap_width:]  # 左图右侧
            roi2 = img2[:, :overlap_width]   # 右图左侧
        else:  # vertical
            # 垂直重叠：使用下侧和上侧区域
            estimated_overlap = (2048 - 1830) / 2048
            overlap_height = max(int(h1 * estimated_overlap), 200)
            roi1 = img1[-overlap_height:, :]  # 上图下侧
            roi2 = img2[:overlap_height, :]   # 下图上侧
        
        return roi1, roi2
    
    def calculate_single_pair_offset(self, pair_info):
        """计算单个图像对的偏移量"""
        (r1, c1), (r2, c2), direction = pair_info
        
        if (r1, c1) not in self.gray_images or (r2, c2) not in self.gray_images:
            return None
        
        img1 = self.gray_images[(r1, c1)]
        img2 = self.gray_images[(r2, c2)]
        
        try:
            # 提取重叠区域
            roi1, roi2 = self.extract_overlap_roi(img1, img2, direction)
            
            # 直接使用相位相关计算（不使用多尺度）
            dx, dy, confidence = self.calculate_phase_correlation(roi1, roi2)
            
            # 调整偏移量到全图坐标系
            if direction == 'horizontal':
                actual_dx = img1.shape[1] - roi1.shape[1] + dx
                actual_dy = dy
            else:  # vertical
                actual_dx = dx
                actual_dy = img1.shape[0] - roi1.shape[0] + dy
            
            return {
                'pair': ((r1, c1), (r2, c2)),
                'offset': (actual_dx, actual_dy, confidence),
                'direction': direction
            }
        except Exception as e:
            print(f"   ⚠️ 配准错误 ({r1},{c1})-({r2},{c2}): {str(e)}")
            return None
    
    def calculate_pairwise_offsets_parallel(self):
        """
        第二阶段：并行计算相邻图像偏移
        
        技术要点：
        - 水平和垂直方向分别处理
        - 相位相关提供亚像素精度配准
        - 多线程并行计算提升效率
        - 实时显示进度和统计信息
        """
        print(f"\n🔍 第二阶段：并行计算相位相关偏移")
        print(f"   🧵 使用 {self.num_threads} 个线程并行处理")
        
        start_time = time.time()
        
        # 准备所有图像对
        tasks = []
        # 水平相邻对
        for r in range(self.rows):
            for c in range(self.cols - 1):
                tasks.append(((r, c), (r, c + 1), 'horizontal'))
        # 垂直相邻对
        for r in range(self.rows - 1):
            for c in range(self.cols):
                tasks.append(((r, c), (r + 1, c), 'vertical'))
        
        print(f"   📊 总计 {len(tasks)} 个图像对")
        
        # 并行处理
        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            future_to_task = {executor.submit(self.calculate_single_pair_offset, task): task for task in tasks}
            
            valid_count = 0
            high_conf_count = 0
            with tqdm(total=len(tasks), desc="🔄 相位相关", unit="对") as pbar:
                for future in as_completed(future_to_task):
                    result = future.result()
                    if result and result['offset'] is not None:
                        dx, dy, conf = result['offset']
                        self.pairwise_offsets[result['pair']] = result['offset']
                        valid_count += 1
                        if conf > 0.7:  # 高置信度计数
                            high_conf_count += 1
                    
                    pbar.set_postfix({'有效': valid_count, '高置信度': high_conf_count, '总数': len(tasks)})
                    pbar.update(1)
        
        calc_time = time.time() - start_time
        print(f"   ✅ 计算完成：{valid_count}/{len(tasks)} 有效匹配，耗时 {calc_time:.2f}s")
        print(f"   🌟 高置信度匹配：{high_conf_count}/{valid_count} ({high_conf_count/max(1,valid_count)*100:.1f}%)")
        print(f"   ⚡ 平均每对耗时：{calc_time/len(tasks):.3f}s")
        
        # 添加可视化检查点
        self._visualize_confidence_map()

    def _visualize_confidence_map(self):
        """可视化配准置信度分布，帮助诊断问题区域"""
        try:
            # 创建置信度热力图
            conf_map = np.zeros((self.rows, self.cols))
            count_map = np.zeros((self.rows, self.cols))
            
            # 收集每个tile的平均置信度
            for pair, (dx, dy, conf) in self.pairwise_offsets.items():
                (r1, c1), (r2, c2) = pair
                conf_map[r1, c1] += conf
                conf_map[r2, c2] += conf
                count_map[r1, c1] += 1
                count_map[r2, c2] += 1
            
            # 计算平均值
            mask = count_map > 0
            conf_map[mask] /= count_map[mask]
            
            # 输出统计信息
            min_conf = np.min(conf_map[mask])
            max_conf = np.max(conf_map[mask])
            mean_conf = np.mean(conf_map[mask])
            
            print(f"   📊 置信度统计：最小 {min_conf:.3f}, 最大 {max_conf:.3f}, 平均 {mean_conf:.3f}")
            
            # 找出低置信度区域
            low_conf_threshold = 0.5
            low_conf_coords = np.where(conf_map < low_conf_threshold)
            if len(low_conf_coords[0]) > 0:
                print(f"   ⚠️  检测到 {len(low_conf_coords[0])} 个低置信度区域:")
                for i in range(min(5, len(low_conf_coords[0]))):  # 最多显示5个
                    r, c = low_conf_coords[0][i], low_conf_coords[1][i]
                    print(f"      位置 ({r},{c}), 置信度: {conf_map[r,c]:.3f}")
                if len(low_conf_coords[0]) > 5:
                    print(f"      ... 以及 {len(low_conf_coords[0])-5} 个更多区域")
        except Exception as e:
            print(f"   ⚠️  置信度可视化失败: {str(e)}")

    # ========================= 第三阶段：全局优化 =========================
    
    def global_optimization(self):
        """
        第三阶段：全局优化消除累积误差
        
        核心思想：
        1. 固定(0,0)图像为坐标原点
        2. 将所有偏移约束转化为优化问题
        3. 使用最小二乘法求解最优位置
        4. 以置信度作为权重
        5. 添加长距离约束，减少累积误差
        6. 添加平滑正则化
        
        优势：
        - 消除逐步累积的误差
        - 全局一致性保证
        - 数值稳定性好
        - 防止网格变形
        """
        print(f"\n🎯 第三阶段：全局优化")
        
        # 创建变量映射（排除固定的(0,0)点）
        tile_to_idx = {}
        idx_to_tile = {}
        idx = 0
        
        for r in range(self.rows):
            for c in range(self.cols):
                if (r, c) in self.color_images and (r, c) != (0, 0):
                    tile_to_idx[(r, c)] = idx
                    idx_to_tile[idx] = (r, c)
                    idx += 1
        
        num_tiles = len(tile_to_idx)
        print(f"   📊 优化变量：{num_tiles} 个图像位置（固定(0,0)为原点）")
        
        # 构建约束方程
        constraints = []
        weights = []
        
        # 1. 相邻图像约束（原有方法）
        for pair, (dx, dy, confidence) in self.pairwise_offsets.items():
            tile1, tile2 = pair
            
            if tile1 == (0, 0) and tile2 in tile_to_idx:
                # 原点到变量的约束
                idx2 = tile_to_idx[tile2]
                constraints.append(('origin_to_var', idx2, dx, dy))
                weights.append(confidence)
            elif tile2 == (0, 0) and tile1 in tile_to_idx:
                # 变量到原点的约束
                idx1 = tile_to_idx[tile1]
                constraints.append(('var_to_origin', idx1, dx, dy))
                weights.append(confidence)
            elif tile1 in tile_to_idx and tile2 in tile_to_idx:
                # 变量间约束
                idx1 = tile_to_idx[tile1]
                idx2 = tile_to_idx[tile2]
                constraints.append(('var_to_var', idx1, idx2, dx, dy))
                weights.append(confidence)
        
        # 2. 添加长距离约束 - 跳跃式连接，减少误差累积
        print("   🔄 添加长距离约束以减少累积误差")
        long_distance_added = 0
        
        # 水平方向跳跃连接
        for r in range(self.rows):
            for c1 in range(self.cols):
                for step in [2, 3, 4]:  # 尝试跨越2、3、4个tiles连接
                    c2 = c1 + step
                    if c2 < self.cols and (r, c1) in self.color_images and (r, c2) in self.color_images:
                        # 如果两个点都存在且不是原点
                        if (r, c1) != (0, 0) and (r, c2) != (0, 0) and (r, c1) in tile_to_idx and (r, c2) in tile_to_idx:
                            # 计算估计偏移，基于已知的相邻偏移
                            est_dx, est_dy = 0, 0
                            valid_path = True
                            
                            for i in range(step):
                                if ((r, c1+i), (r, c1+i+1)) in self.pairwise_offsets:
                                    dx, dy, _ = self.pairwise_offsets[((r, c1+i), (r, c1+i+1))]
                                    est_dx += dx
                                    est_dy += dy
                                else:
                                    valid_path = False
                                    break
                            
                            if valid_path:
                                idx1 = tile_to_idx[(r, c1)]
                                idx2 = tile_to_idx[(r, c2)]
                                
                                # 添加约束，但权重较低（0.3）
                                constraints.append(('var_to_var', idx1, idx2, est_dx, est_dy))
                                weights.append(0.3)
                                long_distance_added += 1
        
        # 垂直方向跳跃连接
        for c in range(self.cols):
            for r1 in range(self.rows):
                for step in [2, 3, 4]:  # 尝试跨越2、3、4个tiles连接
                    r2 = r1 + step
                    if r2 < self.rows and (r1, c) in self.color_images and (r2, c) in self.color_images:
                        # 如果两个点都存在且不是原点
                        if (r1, c) != (0, 0) and (r2, c) != (0, 0) and (r1, c) in tile_to_idx and (r2, c) in tile_to_idx:
                            # 计算估计偏移，基于已知的相邻偏移
                            est_dx, est_dy = 0, 0
                            valid_path = True
                            
                            for i in range(step):
                                if ((r1+i, c), (r1+i+1, c)) in self.pairwise_offsets:
                                    dx, dy, _ = self.pairwise_offsets[((r1+i, c), (r1+i+1, c))]
                                    est_dx += dx
                                    est_dy += dy
                                else:
                                    valid_path = False
                                    break
                            
                            if valid_path:
                                idx1 = tile_to_idx[(r1, c)]
                                idx2 = tile_to_idx[(r2, c)]
                                
                                # 添加约束，但权重较低（0.3）
                                constraints.append(('var_to_var', idx1, idx2, est_dx, est_dy))
                                weights.append(0.3)
                                long_distance_added += 1
        
        # 3. 添加对角线约束
        diagonal_added = 0
        for r in range(self.rows - 1):
            for c in range(self.cols - 1):
                if ((r, c) in self.color_images and (r+1, c+1) in self.color_images and 
                    (r, c+1) in self.color_images and (r+1, c) in self.color_images):
                    
                    # 所有四个角点都存在，可以添加对角约束
                    if ((r, c) != (0, 0) and (r+1, c+1) != (0, 0) and 
                        (r, c) in tile_to_idx and (r+1, c+1) in tile_to_idx):
                        
                        # 计算期望的对角线偏移
                        dx_h1, dy_h1 = 0, 0
                        dx_h2, dy_h2 = 0, 0
                        dx_v1, dy_v1 = 0, 0
                        dx_v2, dy_v2 = 0, 0
                        
                        valid = True
                        
                        # 水平路径1
                        if ((r, c), (r, c+1)) in self.pairwise_offsets:
                            dx_h1, dy_h1, _ = self.pairwise_offsets[((r, c), (r, c+1))]
                        else:
                            valid = False
                            
                        # 垂直路径2
                        if ((r, c+1), (r+1, c+1)) in self.pairwise_offsets:
                            dx_v2, dy_v2, _ = self.pairwise_offsets[((r, c+1), (r+1, c+1))]
                        else:
                            valid = False
                            
                        # 垂直路径1
                        if ((r, c), (r+1, c)) in self.pairwise_offsets:
                            dx_v1, dy_v1, _ = self.pairwise_offsets[((r, c), (r+1, c))]
                        else:
                            valid = False
                            
                        # 水平路径2
                        if ((r+1, c), (r+1, c+1)) in self.pairwise_offsets:
                            dx_h2, dy_h2, _ = self.pairwise_offsets[((r+1, c), (r+1, c+1))]
                        else:
                            valid = False
                        
                        if valid:
                            # 对角线1 (r,c) -> (r+1,c+1)
                            est_dx1 = dx_h1 + dx_v2
                            est_dy1 = dy_h1 + dy_v2
                            
                            # 对角线2（替代路径）
                            est_dx2 = dx_v1 + dx_h2
                            est_dy2 = dy_v1 + dy_h2
                            
                            # 取平均作为对角线约束
                            est_dx = (est_dx1 + est_dx2) / 2
                            est_dy = (est_dy1 + est_dy2) / 2
                            
                            idx1 = tile_to_idx[(r, c)]
                            idx2 = tile_to_idx[(r+1, c+1)]
                            
                            constraints.append(('var_to_var', idx1, idx2, est_dx, est_dy))
                            weights.append(0.25)  # 对角线约束权重较低
                            diagonal_added += 1
        
        print(f"   ➕ 添加了 {long_distance_added} 个长距离约束和 {diagonal_added} 个对角线约束")
        
        # 初始估计（使用多路径平均以减少误差）
        initial_positions = {(0, 0): (0.0, 0.0)}
        
        # 初始化所有可能的起始点（与原点直接相连的点）
        starting_points = []
        for pair, (dx, dy, confidence) in self.pairwise_offsets.items():
            tile1, tile2 = pair
            if tile1 == (0, 0) and tile2 in self.color_images:
                initial_positions[tile2] = (dx, dy)
                starting_points.append(tile2)
            elif tile2 == (0, 0) and tile1 in self.color_images:
                initial_positions[tile1] = (-dx, -dy)
                starting_points.append(tile1)
        
        # 从每个起始点开始传播位置
        if starting_points:
            print(f"   🌱 使用 {len(starting_points)} 个起始点进行初始位置估计")
            
            # 逐步扩展位置估计，直到所有可能的位置都被估计
            positions_to_process = set(starting_points)
            positions_processed = set([(0, 0)])
            
            while positions_to_process:
                next_positions = set()
                
                for current_pos in positions_to_process:
                    positions_processed.add(current_pos)
                    current_x, current_y = initial_positions[current_pos]
                    
                    # 尝试从当前位置向周围扩展
                    for dr in [-1, 0, 1]:
                        for dc in [-1, 0, 1]:
                            if dr == 0 and dc == 0:
                                continue  # 跳过自己
                                
                            neighbor = (current_pos[0] + dr, current_pos[1] + dc)
                            
                            # 检查邻居是否有效
                            if (neighbor[0] < 0 or neighbor[0] >= self.rows or 
                                neighbor[1] < 0 or neighbor[1] >= self.cols or
                                neighbor not in self.color_images or
                                neighbor in positions_processed):
                                continue
                            
                            # 检查是否有直接连接
                            if (current_pos, neighbor) in self.pairwise_offsets:
                                dx, dy, _ = self.pairwise_offsets[(current_pos, neighbor)]
                                new_x = current_x + dx
                                new_y = current_y + dy
                                
                                if neighbor in initial_positions:
                                    # 如果已经有估计，取平均以减少误差
                                    old_x, old_y = initial_positions[neighbor]
                                    initial_positions[neighbor] = ((old_x + new_x) / 2, (old_y + new_y) / 2)
                                else:
                                    initial_positions[neighbor] = (new_x, new_y)
                                    
                                next_positions.add(neighbor)
                            elif (neighbor, current_pos) in self.pairwise_offsets:
                                dx, dy, _ = self.pairwise_offsets[(neighbor, current_pos)]
                                new_x = current_x - dx
                                new_y = current_y - dy
                                
                                if neighbor in initial_positions:
                                    # 如果已经有估计，取平均以减少误差
                                    old_x, old_y = initial_positions[neighbor]
                                    initial_positions[neighbor] = ((old_x + new_x) / 2, (old_y + new_y) / 2)
                                else:
                                    initial_positions[neighbor] = (new_x, new_y)
                                    
                                next_positions.add(neighbor)
                
                positions_to_process = next_positions - positions_processed
        else:
            # 如果没有直接相连的起始点，回退到原来的方法
            # 逐行累加估计
            for c in range(1, self.cols):
                if ((0, c-1), (0, c)) in self.pairwise_offsets:
                    prev_x, prev_y = initial_positions[(0, c-1)]
                    dx, dy, _ = self.pairwise_offsets[((0, c-1), (0, c))]
                    initial_positions[(0, c)] = (prev_x + dx, prev_y + dy)
            
            for r in range(1, self.rows):
                for c in range(self.cols):
                    if ((r-1, c), (r, c)) in self.pairwise_offsets and (r-1, c) in initial_positions:
                        prev_x, prev_y = initial_positions[(r-1, c)]
                        dx, dy, _ = self.pairwise_offsets[((r-1, c), (r, c))]
                        initial_positions[(r, c)] = (prev_x + dx, prev_y + dy)
        
        # 构建优化参数向量
        x0 = np.zeros(2 * num_tiles)
        for tile, (x, y) in initial_positions.items():
            if tile in tile_to_idx:
                idx = tile_to_idx[tile]
                x0[2*idx] = x
                x0[2*idx + 1] = y
        
        # 残差函数，包括平滑正则化
        def residuals(params):
            residual_list = []
            
            # 1. 处理原有的约束
            for i, constraint in enumerate(constraints):
                weight = weights[i]
                
                if constraint[0] == 'origin_to_var':
                    _, idx2, dx_obs, dy_obs = constraint
                    x2, y2 = params[2*idx2], params[2*idx2 + 1]
                    dx_pred, dy_pred = x2, y2
                elif constraint[0] == 'var_to_origin':
                    _, idx1, dx_obs, dy_obs = constraint
                    x1, y1 = params[2*idx1], params[2*idx1 + 1]
                    dx_pred, dy_pred = -x1, -y1
                elif constraint[0] == 'var_to_var':
                    _, idx1, idx2, dx_obs, dy_obs = constraint
                    x1, y1 = params[2*idx1], params[2*idx1 + 1]
                    x2, y2 = params[2*idx2], params[2*idx2 + 1]
                    dx_pred = x2 - x1
                    dy_pred = y2 - y1
                
                residual_list.extend([
                    weight * (dx_pred - dx_obs),
                    weight * (dy_pred - dy_obs)
                ])
            
            # 2. 添加平滑正则化（使网格保持均匀）- 避免局部扭曲
            grid_smoothness_weight = 0.1  # 平滑项权重
            
            for r in range(self.rows):
                for c in range(self.cols - 2):
                    # 水平方向三点约束
                    if ((r, c) in tile_to_idx and (r, c+1) in tile_to_idx and (r, c+2) in tile_to_idx):
                        idx1 = tile_to_idx[(r, c)]
                        idx2 = tile_to_idx[(r, c+1)]
                        idx3 = tile_to_idx[(r, c+2)]
                        
                        x1, y1 = params[2*idx1], params[2*idx1 + 1]
                        x2, y2 = params[2*idx2], params[2*idx2 + 1]
                        x3, y3 = params[2*idx3], params[2*idx3 + 1]
                        
                        # 三点应该近似在一条直线上，中间点偏差作为残差
                        expected_x2 = (x1 + x3) / 2
                        expected_y2 = (y1 + y3) / 2
                        
                        residual_list.extend([
                            grid_smoothness_weight * (x2 - expected_x2),
                            grid_smoothness_weight * (y2 - expected_y2)
                        ])
            
            for c in range(self.cols):
                for r in range(self.rows - 2):
                    # 垂直方向三点约束
                    if ((r, c) in tile_to_idx and (r+1, c) in tile_to_idx and (r+2, c) in tile_to_idx):
                        idx1 = tile_to_idx[(r, c)]
                        idx2 = tile_to_idx[(r+1, c)]
                        idx3 = tile_to_idx[(r+2, c)]
                        
                        x1, y1 = params[2*idx1], params[2*idx1 + 1]
                        x2, y2 = params[2*idx2], params[2*idx2 + 1]
                        x3, y3 = params[2*idx3], params[2*idx3 + 1]
                        
                        # 三点应该近似在一条直线上，中间点偏差作为残差
                        expected_x2 = (x1 + x3) / 2
                        expected_y2 = (y1 + y3) / 2
                        
                        residual_list.extend([
                            grid_smoothness_weight * (x2 - expected_x2),
                            grid_smoothness_weight * (y2 - expected_y2)
                        ])
            
            return np.array(residual_list)
        
        # 执行优化（使用信赖域反射算法，比Levenberg-Marquardt更稳健）
        start_time = time.time()
        result = least_squares(residuals, x0, method='trf', loss='soft_l1')
        opt_time = time.time() - start_time
        
        # 提取优化结果
        self.final_positions = {(0, 0): (0.0, 0.0)}
        
        if result.success:
            for idx in range(num_tiles):
                tile = idx_to_tile[idx]
                x_opt = result.x[2*idx]
                y_opt = result.x[2*idx + 1]
                self.final_positions[tile] = (x_opt, y_opt)
            print(f"   ✅ 优化收敛成功，耗时 {opt_time:.3f}s")
            print(f"   🎯 最终误差: {result.cost:.4f}")
        else:
            print(f"   ⚠️  优化未收敛，使用初始估计")
            for tile, pos in initial_positions.items():
                if tile != (0, 0):
                    self.final_positions[tile] = pos

    # ========================= 第四阶段：图像融合 =========================
    
    def calculate_canvas_size(self):
        """计算拼接画布尺寸"""
        if not self.final_positions:
            raise ValueError("未计算图像位置")
        
        sample_img = next(iter(self.color_images.values()))
        img_height, img_width = sample_img.shape[:2]
        
        # 计算边界
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        for (r, c), (x, y) in self.final_positions.items():
            if (r, c) in self.color_images:
                corners = [(x, y), (x + img_width, y), (x, y + img_height), (x + img_width, y + img_height)]
                for corner_x, corner_y in corners:
                    min_x, max_x = min(min_x, corner_x), max(max_x, corner_x)
                    min_y, max_y = min(min_y, corner_y), max(max_y, corner_y)
        
        canvas_width = int(np.ceil(max_x - min_x))
        canvas_height = int(np.ceil(max_y - min_y))
        offset_x, offset_y = -min_x, -min_y
        
        return canvas_width, canvas_height, offset_x, offset_y
    
    def create_distance_weight_map(self, img_shape):
        """创建基于距离的权重图"""
        h, w = img_shape[:2]
        
        # 创建距离场
        y_indices, x_indices = np.ogrid[:h, :w]
        
        # 计算到边界的距离
        dist_to_top = y_indices
        dist_to_bottom = h - 1 - y_indices
        dist_to_left = x_indices
        dist_to_right = w - 1 - x_indices
        
        # 距离权重图
        dist_to_edge = np.minimum(
            np.minimum(dist_to_top, dist_to_bottom),
            np.minimum(dist_to_left, dist_to_right)
        )
        
        # 设置羽化参数
        feather_pixels = min(self.max_feather_pixels, min(h, w) // 10)
        feather_pixels = max(feather_pixels, 5)
        
        if feather_pixels > 5:
            sigma = feather_pixels / 3.0
            weight_map = np.exp(-((feather_pixels - dist_to_edge) ** 2) / (2 * sigma ** 2))
            weight_map = np.clip(weight_map, 0.1, 1.0)
            
            inner_mask = dist_to_edge >= feather_pixels
            weight_map[inner_mask] = 1.0
        else:
            weight_map = np.ones((h, w), dtype=np.float32)
        
        return weight_map.astype(np.float32)
    
    def blend_images_intelligent(self, output_path):
        """
        第四阶段：智能图像融合 - 优先GPU加速
        
        技术特点：
        1. 优先使用GPU分块融合
        2. GPU不可用时回退到CPU高性能模式
        3. 亚像素精度变换
        4. Linear blending权重融合
        """
        print(f"\n🎨 第四阶段：智能图像融合")
        
        canvas_width, canvas_height, offset_x, offset_y = self.calculate_canvas_size()
        memory_gb = (canvas_width * canvas_height * 3 * 8) / (1024**3)
        
        print(f"   📏 画布尺寸：{canvas_width} × {canvas_height}")
        print(f"   💾 内存需求：{memory_gb:.2f} GB")
        
        # 优先尝试GPU融合
        if torch.cuda.is_available() and self.use_gpu:
            print(f"   🎮 使用GPU加速融合模式")
            return self._blend_gpu_accelerated(canvas_width, canvas_height, offset_x, offset_y, output_path)
        else:
            print(f"   💾 GPU不可用，使用CPU内存高效模式")
            return self._blend_memory_efficient(canvas_width, canvas_height, offset_x, offset_y, output_path)

    def _blend_gpu_accelerated(self, canvas_width, canvas_height, offset_x, offset_y, output_path):
        """GPU加速图像融合"""
        start_time = time.time()
        
        # 清理GPU缓存
        torch.cuda.empty_cache()
        
        # 计算内存需求
        required_memory_gb = (canvas_width * canvas_height * 4 * 4) / (1024**3)  # float32 * 4 channels
        chunk_memory_gb = (self.gpu_chunk_size * self.gpu_chunk_size * 4 * 4) / (1024**3)
        
        print(f"   🔧 GPU配置:")
        print(f"      显存限制: {self.gpu_memory_limit_gb:.1f} GB")
        print(f"      预估需求: {required_memory_gb:.1f} GB")
        print(f"      分块大小: {self.gpu_chunk_size} x {self.gpu_chunk_size}")
        print(f"      分块内存: {chunk_memory_gb:.1f} GB")
        
        # 强制使用分块模式以适应4GB显存
        return self._blend_gpu_chunked(canvas_width, canvas_height, offset_x, offset_y, output_path)

    def _blend_gpu_chunked(self, canvas_width, canvas_height, offset_x, offset_y, output_path):
        """GPU分块融合 - 专为4GB显存优化"""
        print(f"   🧩 GPU分块融合模式")
        
        start_time = time.time()
        device = torch.device('cuda')
        chunk_size = self.gpu_chunk_size
        
        try:
            # 创建最终结果画布（在CPU上）
            result = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)
            
            # 计算分块数量
            y_chunks = (canvas_height + chunk_size - 1) // chunk_size
            x_chunks = (canvas_width + chunk_size - 1) // chunk_size
            total_chunks = y_chunks * x_chunks
            
            print(f"      分块数量: {y_chunks} x {x_chunks} = {total_chunks}")
            
            # 预处理图像位置信息
            image_positions = {}
            for (r, c), (x, y) in self.final_positions.items():
                if (r, c) in self.color_images:
                    img = self.color_images[(r, c)]
                    h, w = img.shape[:2]
                    x_adj = int(x + offset_x)
                    y_adj = int(y + offset_y)
                    image_positions[(r, c)] = {
                        'img': img,
                        'x': x_adj, 'y': y_adj, 
                        'h': h, 'w': w,
                        'weight_map': self.create_distance_weight_map(img.shape)
                    }
            
            # 使用进度条显示分块处理进度
            with tqdm(total=total_chunks, desc="🧩 GPU分块融合", ncols=80) as pbar:
                chunk_count = 0
                for y_start in range(0, canvas_height, chunk_size):
                    y_end = min(y_start + chunk_size, canvas_height)
                    
                    for x_start in range(0, canvas_width, chunk_size):
                        x_end = min(x_start + chunk_size, canvas_width)
                        chunk_count += 1
                        
                        chunk_h = y_end - y_start
                        chunk_w = x_end - x_start
                        
                        # 清理GPU缓存
                        torch.cuda.empty_cache()
                        
                        # 在GPU上创建块画布
                        chunk_canvas = torch.zeros((chunk_h, chunk_w, 3), dtype=torch.float32, device=device)
                        chunk_weight = torch.zeros((chunk_h, chunk_w), dtype=torch.float32, device=device)
                        
                        # 处理与当前块重叠的图像
                        images_in_chunk = 0
                        for (r, c), img_info in image_positions.items():
                            img = img_info['img']
                            img_x, img_y = img_info['x'], img_info['y']
                            img_h, img_w = img_info['h'], img_info['w']
                            weight_map = img_info['weight_map']
                            
                            # 检查图像是否与当前块重叠
                            if (img_x + img_w <= x_start or img_x >= x_end or 
                                img_y + img_h <= y_start or img_y >= y_end):
                                continue
                            
                            images_in_chunk += 1
                            
                            # 计算重叠区域
                            overlap_x1 = max(img_x, x_start)
                            overlap_y1 = max(img_y, y_start)
                            overlap_x2 = min(img_x + img_w, x_end)
                            overlap_y2 = min(img_y + img_h, y_end)
                            
                            if overlap_x2 <= overlap_x1 or overlap_y2 <= overlap_y1:
                                continue
                            
                            # 在图像中的坐标
                            img_x1 = overlap_x1 - img_x
                            img_y1 = overlap_y1 - img_y
                            img_x2 = overlap_x2 - img_x
                            img_y2 = overlap_y2 - img_y
                            
                            # 在块中的坐标
                            chunk_x1 = overlap_x1 - x_start
                            chunk_y1 = overlap_y1 - y_start
                            chunk_x2 = overlap_x2 - x_start
                            chunk_y2 = overlap_y2 - y_start
                            
                            # 提取图像和权重区域
                            img_roi = img[img_y1:img_y2, img_x1:img_x2].astype(np.float32)
                            weight_roi = weight_map[img_y1:img_y2, img_x1:img_x2]
                            
                            # 转换到GPU
                            img_tensor = torch.from_numpy(img_roi).to(device)
                            weight_tensor = torch.from_numpy(weight_roi).to(device)
                            
                            # 融合到块画布
                            for ch in range(3):
                                chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2, ch] += img_tensor[:, :, ch] * weight_tensor
                            
                            chunk_weight[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += weight_tensor
                        
                        # 归一化块
                        chunk_weight = torch.clamp(chunk_weight, min=1e-10)
                        
                        chunk_result = torch.zeros_like(chunk_canvas)
                        for ch in range(3):
                            chunk_result[:, :, ch] = chunk_canvas[:, :, ch] / chunk_weight
                        
                        chunk_result = torch.clamp(chunk_result, 0, 255)
                        chunk_final = chunk_result.byte().cpu().numpy()
                        
                        # 保存到最终结果
                        result[y_start:y_end, x_start:x_end] = chunk_final
                        
                        # 更新进度条
                        pbar.set_postfix({
                            '块': f'{chunk_count}/{total_chunks}', 
                            '图像': images_in_chunk,
                            'GPU内存': f'{torch.cuda.memory_allocated()/1024**2:.0f}MB'
                        })
                        pbar.update(1)
                        
                        # 清理GPU内存
                        del chunk_canvas, chunk_weight, chunk_result, chunk_final, img_tensor, weight_tensor
                        torch.cuda.empty_cache()
            
            # 保存结果
            cv2.imwrite(output_path, result, [cv2.IMWRITE_JPEG_QUALITY, 85])
            
            blend_time = time.time() - start_time
            print(f"   ✅ GPU分块融合完成，耗时 {blend_time:.2f}s")
            return result
            
        except Exception as e:
            print(f"   ❌ GPU融合失败: {e}")
            print(f"   🔄 回退到CPU内存高效模式")
            torch.cuda.empty_cache()
            return self._blend_memory_efficient(canvas_width, canvas_height, offset_x, offset_y, output_path)
    

    
    def _blend_memory_efficient(self, canvas_width, canvas_height, offset_x, offset_y, output_path):
        """内存高效融合"""
        print(f"   💾 使用内存高效模式")
        start_time = time.time()
        
        canvas = np.zeros((canvas_height, canvas_width, 3), dtype=np.float32)
        weight_canvas = np.zeros((canvas_height, canvas_width), dtype=np.float32)
        
        with tqdm(total=len(self.final_positions), desc="🎨 融合图像", unit="张") as pbar:
            for (r, c), (x, y) in self.final_positions.items():
                if (r, c) not in self.color_images:
                    pbar.update(1)
                    continue
                
                img = self.color_images[(r, c)]
                h, w = img.shape[:2]
                
                # 计算放置位置
                x_adj = (x + offset_x)
                y_adj = (y + offset_y)
                weight_map = self.create_distance_weight_map(img.shape)
                
                # 计算覆盖区域
                x_start = int(np.floor(x_adj))
                y_start = int(np.floor(y_adj))
                x_end = min(x_start + w, canvas_width)
                y_end = min(y_start + h, canvas_height)
                
                if x_start < canvas_width and y_start < canvas_height and x_end > 0 and y_end > 0:
                    # 计算有效区域
                    img_x_start = max(0, -x_start)
                    img_y_start = max(0, -y_start)
                    img_x_end = img_x_start + (x_end - max(0, x_start))
                    img_y_end = img_y_start + (y_end - max(0, y_start))
                    
                    canvas_x_start = max(0, x_start)
                    canvas_y_start = max(0, y_start)
                    
                    if img_x_end > img_x_start and img_y_end > img_y_start:
                        img_region = img[img_y_start:img_y_end, img_x_start:img_x_end]
                        weight_region = weight_map[img_y_start:img_y_end, img_x_start:img_x_end]
                        
                        # 累积到画布
                        for c_idx in range(3):
                            canvas[canvas_y_start:canvas_y_start + img_region.shape[0], 
                                  canvas_x_start:canvas_x_start + img_region.shape[1], c_idx] += (
                                img_region[:, :, c_idx].astype(np.float32) * weight_region
                            )
                        
                        weight_canvas[canvas_y_start:canvas_y_start + img_region.shape[0], 
                                     canvas_x_start:canvas_x_start + img_region.shape[1]] += weight_region
                
                pbar.update(1)
        
        # 归一化
        mask = weight_canvas > 0
        result = np.zeros_like(canvas, dtype=np.uint8)
        for c_idx in range(3):
            result[:, :, c_idx][mask] = (canvas[:, :, c_idx][mask] / weight_canvas[mask]).astype(np.uint8)
        
        cv2.imwrite(output_path, result, [cv2.IMWRITE_JPEG_QUALITY, 85])
        
        blend_time = time.time() - start_time
        print(f"   ✅ 内存高效融合完成，耗时 {blend_time:.2f}s")
        return result

    # ========================= 主要接口和工具函数 =========================
    
    def save_configuration(self, filename="TileConfiguration_opencv_subpixel.txt"):
        """保存配置文件"""
        with open(filename, 'w') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n\n")
            f.write("# Define the image coordinates (subpixel precision)\n")
            
            for (r, c), (x, y) in self.final_positions.items():
                if (r, c) in self.color_images:
                    f.write(f"r{r:03d}_c{c:03d}.jpg; ; ({x:.6f}, {y:.6f})\n")
        
        print(f"📄 配置文件已保存: {filename}")
    
    def save_registered_configuration(self, filename="TileConfiguration.registered-python.txt"):
        """
        保存配准后的配置文件
        
        Args:
            filename: 输出文件名，默认为TileConfiguration.registered-python.txt
        """
        print(f"\n📄 保存配准后配置文件: {filename}")
        
        with open(filename, 'w') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n\n")
            f.write("# Define the image coordinates (subpixel precision registration by Python)\n")
            
            # 确定文件名格式
            if self.image_naming_format == "s_sequential" and hasattr(self, 'initial_positions'):
                # 对于s_格式，需要反向映射回原始文件名
                config_path = os.path.join(self.image_dir, "TileConfiguration.txt")
                if os.path.exists(config_path):
                    grid_info = self.parse_tile_configuration(config_path)
                    coordinate_map = grid_info['coordinate_map']
                    
                    # 创建反向映射
                    grid_to_filename = {}
                    for img_filename, (grid_r, grid_c) in coordinate_map.items():
                        grid_to_filename[(grid_r, grid_c)] = img_filename
                else:
                    print("   ⚠️  未找到TileConfiguration.txt文件，使用默认格式")
                    grid_to_filename = {}
                
                # 按照原始文件顺序输出
                sorted_positions = []
                for (r, c), (x, y) in self.final_positions.items():
                    if (r, c) in self.color_images and (r, c) in grid_to_filename:
                        img_filename = grid_to_filename[(r, c)]
                        # 提取数字部分用于排序
                        try:
                            file_num = int(img_filename.split('_')[1].split('.')[0])
                            sorted_positions.append((file_num, img_filename, x, y))
                        except (IndexError, ValueError):
                            print(f"   ⚠️ 无法解析文件名: {img_filename}")
                            continue
                
                # 按文件编号排序
                sorted_positions.sort(key=lambda x: x[0])
                
                for _, img_filename, x, y in sorted_positions:
                    f.write(f"{img_filename}; ; ({x:.6f}, {y:.6f})\n")
            else:
                # 对于r000_c000格式，按行列顺序输出
                for r in range(self.rows):
                    for c in range(self.cols):
                        if (r, c) in self.final_positions and (r, c) in self.color_images:
                            x, y = self.final_positions[(r, c)]
                            f.write(f"r{r:03d}_c{c:03d}.jpg; ; ({x:.6f}, {y:.6f})\n")
        
        print(f"   ✅ 配准文件已保存: {filename}")
        
        # 如果存在原始TileConfiguration.txt文件，进行比较分析
        original_config = os.path.join(self.image_dir, "TileConfiguration.txt")
        if os.path.exists(original_config):
            try:
                self._compare_configurations(original_config, filename)
            except Exception as e:
                print(f"   ⚠️  配置比较失败: {str(e)}")
                import traceback
                traceback.print_exc()
    
    def _compare_configurations(self, original_file, registered_file):
        """比较原始配置和配准后配置的差异"""
        print(f"\n📊 配置文件比较分析:")
        
        try:
            # 解析原始配置
            original_positions = {}
            with open(original_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('#') or line.startswith('dim') or not line:
                        continue
                    match = re.match(r'(\S+)\s*;\s*;\s*\(([-\d.]+),\s*([-\d.]+)\)', line)
                    if match:
                        filename = match.group(1)
                        x = float(match.group(2))
                        y = float(match.group(3))
                        original_positions[filename] = (x, y)
            
            # 解析配准后配置
            registered_positions = {}
            with open(registered_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('#') or line.startswith('dim') or not line:
                        continue
                    match = re.match(r'(\S+)\s*;\s*;\s*\(([-\d.]+),\s*([-\d.]+)\)', line)
                    if match:
                        filename = match.group(1)
                        x = float(match.group(2))
                        y = float(match.group(3))
                        registered_positions[filename] = (x, y)
            
            # 计算偏移差异
            max_diff = 0
            avg_diff = 0
            diff_count = 0
            
            for filename in original_positions:
                if filename in registered_positions:
                    orig_x, orig_y = original_positions[filename]
                    reg_x, reg_y = registered_positions[filename]
                    
                    diff = np.sqrt((orig_x - reg_x)**2 + (orig_y - reg_y)**2)
                    max_diff = max(max_diff, diff)
                    avg_diff += diff
                    diff_count += 1
            
            if diff_count > 0:
                avg_diff /= diff_count
                
                print(f"   📏 位置调整统计:")
                print(f"      最大调整距离: {max_diff:.2f} 像素")
                print(f"      平均调整距离: {avg_diff:.2f} 像素")
                print(f"      调整图像数量: {diff_count} 张")
                
                if avg_diff < 10:
                    print(f"   ✅ 调整幅度较小，原始配置质量较好")
                elif avg_diff < 50:
                    print(f"   📝 调整幅度中等，配准带来明显改善")
                else:
                    print(f"   🔧 调整幅度较大，原始配置存在较大误差")
        
        except Exception as e:
            print(f"   ⚠️  配置比较失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    
    
    def run_complete_stitching(self, output_path=None):
        """
        执行完整的图像拼接流程
        
        返回: (最终位置字典, 拼接结果图像)
        """
        if output_path is None:
            output_path = f"{self.image_dir}_stitched_advanced.jpg"
        
        total_start = time.time()
        
        print("🚀 " + "="*70)
        print("🚀 高性能图像拼接 - 亚像素精度配准")
        print("🚀 " + "="*70)
        
        try:
            # 第一阶段：加载图像
            step1_start = time.time()
            self.load_images_parallel()
            step1_time = time.time() - step1_start
            
            # 第二阶段：相位相关
            step2_start = time.time()
            self.calculate_pairwise_offsets_parallel()
            step2_time = time.time() - step2_start
            
            # 第三阶段：全局优化
            step3_start = time.time()
            self.global_optimization()
            step3_time = time.time() - step3_start
            
            # 保存配置文件
            self.save_configuration()
            
            # 保存配准后的配置文件
            self.save_registered_configuration()
            
            # 第四阶段：图像融合
            step4_start = time.time()
            result_image = self.blend_images_intelligent(output_path)
            step4_time = time.time() - step4_start
            
            # 总结
            total_time = time.time() - total_start
            print("\n🏁 " + "="*70)
            print("🏁 拼接完成!")
            print("🏁 " + "="*70)
            print(f"⏱️  阶段1 (图像加载):     {step1_time:8.2f}s")
            print(f"⏱️  阶段2 (相位相关):     {step2_time:8.2f}s") 
            print(f"⏱️  阶段3 (全局优化):     {step3_time:8.2f}s")
            print(f"⏱️  阶段4 (图像融合):     {step4_time:8.2f}s")
            print(f"⏱️  总耗时:              {total_time:8.2f}s")
            print("🏁 " + "="*70)
            print(f"📸 结果保存至: {output_path}")
            
            return self.final_positions, result_image
            
        except Exception as e:
            print(f"❌ 拼接失败: {str(e)}")
            raise

    def calculate_phase_correlation_multiscale(self, img1, img2, levels=3):
        """
        多尺度相位相关算法
        
        参数:
            img1, img2: 输入图像
            levels: 金字塔层级数
            
        返回:
            dx, dy: 亚像素精度偏移量
            confidence: 匹配置信度
        """
        # 如果图像太小，不构建金字塔
        min_dim = min(min(img1.shape), min(img2.shape))
        actual_levels = min(levels, int(np.log2(min_dim / 32)) + 1)
        actual_levels = max(1, actual_levels)
        
        # 构建金字塔
        pyramid1 = self.build_pyramid(img1, actual_levels)
        pyramid2 = self.build_pyramid(img2, actual_levels)
        
        # 从粗到精策略
        dx, dy = 0, 0  # 初始偏移估计
        final_confidence = 0
        
        for level in range(actual_levels - 1, -1, -1):
            curr_img1 = pyramid1[level]
            curr_img2 = pyramid2[level]
            
            # 根据当前尺度调整搜索范围
            if level < actual_levels - 1:
                # 在细尺度上，根据粗尺度结果搜索
                dx *= 2
                dy *= 2
                max_shift = max(20, min(curr_img1.shape) // 10)  # 限制在一个合理范围
            else:
                # 在最粗尺度上，进行全局搜索
                max_shift = min(curr_img1.shape) // 2
            
            # 基于之前结果的搜索偏移量（只用于非最粗层级）
            if level < actual_levels - 1:
                # 裁剪搜索区域
                h1, w1 = curr_img1.shape
                h2, w2 = curr_img2.shape
                
                # 确保不超出边界
                search_x = int(np.clip(dx, -max_shift, max_shift))
                search_y = int(np.clip(dy, -max_shift, max_shift))
                
                # 确定搜索窗口
                y1_start = max(0, search_y)
                y1_end = min(h1, h1 + search_y)
                x1_start = max(0, search_x)
                x1_end = min(w1, w1 + search_x)
                
                y2_start = max(0, -search_y)
                y2_end = min(h2, h2 - search_y)
                x2_start = max(0, -search_x)
                x2_end = min(w2, w2 - search_x)
                
                # 提取搜索区域
                search_h = min(y1_end - y1_start, y2_end - y2_start)
                search_w = min(x1_end - x1_start, x2_end - x2_start)
                
                if search_h <= 0 or search_w <= 0:
                    # 搜索区域无效，使用整个图像
                    level_dx, level_dy, level_conf = self.calculate_phase_correlation(curr_img1, curr_img2)
                else:
                    # 提取搜索区域
                    roi1 = curr_img1[y1_start:y1_start+search_h, x1_start:x1_start+search_w]
                    roi2 = curr_img2[y2_start:y2_start+search_h, x2_start:x2_start+search_w]
                    
                    level_dx, level_dy, level_conf = self.calculate_phase_correlation(roi1, roi2)
                    
                    # 调整为全局坐标
                    level_dx += search_x + (x2_start - x1_start)
                    level_dy += search_y + (y2_start - y1_start)
            else:
                # 在最粗尺度上，使用全局相位相关
                level_dx, level_dy, level_conf = self.calculate_phase_correlation(curr_img1, curr_img2)
            
            # 更新当前估计
            dx = level_dx
            dy = level_dy
            final_confidence = level_conf
        
        return dx, dy, final_confidence

    def build_pyramid(self, image, levels):
        """构建高斯金字塔"""
        pyramid = [image]
        current = image.copy()
        
        for i in range(1, levels):
            # 高斯模糊 + 下采样
            blurred = cv2.GaussianBlur(current, (5, 5), 1.0)
            downsampled = cv2.resize(blurred, 
                                  (current.shape[1]//2, current.shape[0]//2),
                                  interpolation=cv2.INTER_AREA)
            pyramid.append(downsampled)
            current = downsampled
        
        return pyramid

def main():
    """
    主函数 - 支持两种输入模式
    
    模式1: 直接指定参数
    模式2: 使用TileConfiguration.txt文件
    """
    
    # ================== 配置选择 ==================
    # 模式选择: 1=手动参数, 2=配置文件
    INPUT_MODE = 2
    
    # 模式1: 手动指定参数
    if INPUT_MODE == 1:
        MODE = "GPU"                    # "GPU" 或 "CPU"
        IMAGE_DIR = "test02"           # 图像文件夹
        ROWS, COLS = 6, 16             # 网格尺寸
        OVERLAP = 0.1                  # 重叠度 (0.1 = 10%)
        THREADS = 0                    # 线程数 (0=自动检测全部线程)
        TILE_CONFIG = None
    
    # 模式2: 使用TileConfiguration.txt文件
    elif INPUT_MODE == 2:
        MODE = "GPU"                    # "GPU" 或 "CPU"
        IMAGE_DIR = "Image_SX88"       # 图像文件夹
        TILE_CONFIG = os.path.join(IMAGE_DIR, "TileConfiguration.txt")  # 配置文件路径
        THREADS = 0                    # 线程数 (0=自动检测全部线程)
        # 网格信息将从配置文件中自动解析
    
    else:
        print("❌ 无效的输入模式!")
        return
    # ============================================
    
    print("🚀 高性能图像拼接系统")
    print("=" * 50)
    print(f"📋 输入模式: {INPUT_MODE} ({'手动参数' if INPUT_MODE == 1 else 'TileConfiguration.txt'})")
    
    # CPU线程检测
    max_threads = multiprocessing.cpu_count()
    if THREADS == 0:
        num_threads = max_threads
        print(f"🖥️  CPU: {max_threads}线程 (使用全部)")
    else:
        num_threads = min(THREADS, max_threads)
        print(f"🖥️  CPU: {max_threads}线程 (使用{num_threads})")
    
    # GPU检测
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"🎮 GPU: {gpu_name} ({gpu_memory:.1f}GB)")
    else:
        print("❌ 无GPU，将使用CPU模式")
        MODE = "CPU"
    
    # 显示基本配置
    print(f"🔧 融合模式: {MODE}")
    print(f"📁 图像目录: {IMAGE_DIR}")
    print(f"🧵 并行线程: {num_threads}")
    
    try:
        # 创建拼接器
        if INPUT_MODE == 1:
            # 模式1: 手动参数
            print(f"🎯 网格尺寸: {ROWS}×{COLS} = {ROWS*COLS}张图像")
            print(f"📐 重叠度: {OVERLAP*100:.1f}%")
            
            stitcher = GridStitcher(
                image_dir=IMAGE_DIR,
                rows=ROWS, 
                cols=COLS,
                overlap_ratio=OVERLAP,
                num_threads=num_threads
            )
        
        elif INPUT_MODE == 2:
            # 模式2: 配置文件
            if not os.path.exists(TILE_CONFIG):
                raise FileNotFoundError(f"配置文件不存在: {TILE_CONFIG}")
            
            print(f"📋 配置文件: {TILE_CONFIG}")
            
            stitcher = GridStitcher(
                image_dir=IMAGE_DIR,
                num_threads=num_threads,
                tile_config_path=TILE_CONFIG
            )
        
        # 设置融合模式
        if MODE == "GPU":
            stitcher.use_gpu = True
            print(f"   🎮 GPU配置: {stitcher.gpu_memory_limit_gb}GB显存, {stitcher.gpu_chunk_size}x{stitcher.gpu_chunk_size}分块")
        else:
            stitcher.use_gpu = False
            print("   💾 CPU内存高效模式")
            # 强制CPU模式
            def cpu_blend(output_path):
                canvas_width, canvas_height, offset_x, offset_y = stitcher.calculate_canvas_size()
                return stitcher._blend_memory_efficient(canvas_width, canvas_height, offset_x, offset_y, output_path)
            stitcher.blend_images_intelligent = cpu_blend
        
        # 执行拼接
        print(f"\n🚀 开始拼接...")
        positions, result_image = stitcher.run_complete_stitching()
        
        # 显示结果
        output_path = f"{IMAGE_DIR}_stitched_advanced.jpg"
        file_size_mb = os.path.getsize(output_path) / (1024 * 1024)
        
        print(f"\n✅ 拼接完成!")
        print(f"📊 图像尺寸: {result_image.shape}")
        print(f"🎯 处理图像: {len(positions)}张")
        print(f"💾 文件大小: {file_size_mb:.1f}MB")
        print(f"📄 配准文件: TileConfiguration.registered-python.txt")
        
    except Exception as e:
        print(f"\n❌ 拼接失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
