import cv2
import numpy as np
import os
import re
import time
import multiprocessing
# from scipy.optimize import least_squares  # 不再需要，使用ImageJ风格的迭代优化
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import torch
import torch.nn.functional as F


class StitchingConfig:
    """拼接配置类 - 统一管理所有参数"""
    
    def __init__(self):
        # 基本参数
        self.image_dir = None
        self.rows = 3
        self.cols = 3
        self.overlap_ratio = 0.1
        self.num_threads = multiprocessing.cpu_count()
        
        # 输入格式
        self.tile_config_path = None
        self.image_naming_format = "r_c"  # "r_c" 或 "s_sequential"
        
        # GPU配置
        self.use_gpu = torch.cuda.is_available()
        self.gpu_memory_limit_gb = 3.8
        self.gpu_chunk_size = 13000
        
        # 融合参数
        self.max_feather_pixels = 50
        self.memory_limit_gb = 30
        
        # 优化参数 - ImageJ风格
        self.confidence_threshold = 0.3
        self.relative_error_threshold = 2.0  
        self.absolute_error_threshold = 5.0
    
    @classmethod
    def from_directory(cls, image_dir, rows=None, cols=None, overlap_ratio=0.1):
        """从目录创建配置"""
        config = cls()
        config.image_dir = image_dir
        if rows: config.rows = rows
        if cols: config.cols = cols
        config.overlap_ratio = overlap_ratio
        return config
    
    @classmethod
    def from_tile_config(cls, image_dir, tile_config_path):
        """从TileConfiguration.txt文件创建配置"""
        config = cls()
        config.image_dir = image_dir
        config.tile_config_path = tile_config_path
        return config


class ImageLoader:
    """图像加载器 - 处理图像加载和格式解析"""
    
    def __init__(self, config):
        self.config = config
        self.color_images = {}
        self.gray_images = {}
        
    def load_all_images(self):
        """加载所有图像"""
        print(f"\n📋 第一阶段：并行加载图像")
        print(f"   🧵 使用 {self.config.num_threads} 个线程并行加载")
        
        # 构建文件名映射
        grid_to_filename = self._build_filename_mapping()
        
        def load_single_image(r, c):
            """加载单张图像"""
            filename = self._get_filename(r, c, grid_to_filename)
            if not filename:
                return (r, c), None, None, False
                
            filepath = os.path.join(self.config.image_dir, filename)
            if os.path.exists(filepath):
                color_img = cv2.imread(filepath, cv2.IMREAD_COLOR)
                if color_img is not None:
                    gray_img = cv2.cvtColor(color_img, cv2.COLOR_BGR2GRAY)
                    return (r, c), color_img, gray_img, True
            return (r, c), None, None, False
        
        # 并行加载
        start_time = time.time()
        tasks = [(r, c) for r in range(self.config.rows) for c in range(self.config.cols)]
        
        with ThreadPoolExecutor(max_workers=self.config.num_threads) as executor:
            future_to_pos = {executor.submit(load_single_image, r, c): (r, c) for r, c in tasks}
            
            with tqdm(total=len(tasks), desc="🖼️  加载图像", unit="张") as pbar:
                for future in as_completed(future_to_pos):
                    (r, c), color_img, gray_img, success = future.result()
                    if success:
                        self.color_images[(r, c)] = color_img
                        self.gray_images[(r, c)] = gray_img
                    pbar.update(1)
        
        load_time = time.time() - start_time
        print(f"   ✅ 加载完成：{len(self.color_images)} 张图像，耗时 {load_time:.2f}s")
        
        if not self.color_images:
            raise ValueError("❌ 未找到任何图像文件！")
    
    def _build_filename_mapping(self):
        """构建文件名映射"""
        if not self.config.tile_config_path:
            return {}
            
        # 解析TileConfiguration.txt
        grid_info = self._parse_tile_configuration()
        self.config.rows = grid_info['rows']
        self.config.cols = grid_info['cols']
        self.config.overlap_ratio = grid_info['overlap_ratio']
        self.config.image_naming_format = grid_info['naming_format']
        
        return grid_info.get('grid_to_filename', {})
    
    def _parse_tile_configuration(self):
        """解析TileConfiguration.txt文件"""
        config_path = self.config.tile_config_path
        print(f"   📋 解析配置文件: {config_path}")
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        positions = {}
        with open(config_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('#') or line.startswith('dim') or not line:
                    continue
                
                match = re.match(r'(\S+)\s*;\s*;\s*\(([-\d.]+),\s*([-\d.]+)\)', line)
                if match:
                    filename = match.group(1)
                    x = float(match.group(2))
                    y = float(match.group(3))
                    positions[filename] = (x, y)
        
        return self._analyze_grid_structure(positions)
    
    def _analyze_grid_structure(self, positions):
        """分析网格结构"""
        coords = list(positions.values())
        x_coords = sorted(list(set([coord[0] for coord in coords])))
        y_coords = sorted(list(set([coord[1] for coord in coords])))
        
        rows, cols = len(y_coords), len(x_coords)
        
        # 计算重叠度
        if len(x_coords) > 1:
            x_spacing = x_coords[1] - x_coords[0]
            overlap_ratio = max(0, (2448 - x_spacing) / 2448)  # 假设图像宽度2448
        else:
            overlap_ratio = 0.1
        
        # 检测命名格式
        sample_filename = list(positions.keys())[0]
        naming_format = "s_sequential" if sample_filename.startswith('s_') else "r_c"
        
        # 构建网格到文件名的映射
        grid_to_filename = {}
        if naming_format == "s_sequential":
            sorted_files = sorted(positions.keys(), key=lambda x: int(x.split('_')[1].split('.')[0]))
            for idx, filename in enumerate(sorted_files):
                row = idx // cols
                col = (idx % cols) if row % 2 == 0 else (cols - 1 - (idx % cols))
                grid_to_filename[(row, col)] = filename
        
        print(f"   ✅ 检测到 {rows}×{cols} 网格，重叠度 {overlap_ratio*100:.1f}%")
        
        return {
            'rows': rows,
            'cols': cols,
            'overlap_ratio': overlap_ratio,
            'naming_format': naming_format,
            'grid_to_filename': grid_to_filename
        }
    
    def _get_filename(self, r, c, grid_to_filename):
        """获取指定位置的文件名"""
        if grid_to_filename and (r, c) in grid_to_filename:
            return grid_to_filename[(r, c)]
        else:
            return f"r{r:03d}_c{c:03d}.jpg"


class PhaseCorrelationMatcher:
    """相位相关匹配器 - 处理图像配准"""
    
    def __init__(self, config):
        self.config = config
        self.pairwise_offsets = {}
    
    def calculate_all_offsets(self, gray_images):
        """计算所有相邻图像的偏移量"""
        print(f"\n🔍 第二阶段：并行计算相位相关偏移")
        
        # 准备所有图像对
        tasks = []
        for r in range(self.config.rows):
            for c in range(self.config.cols - 1):
                tasks.append(((r, c), (r, c + 1), 'horizontal'))
        for r in range(self.config.rows - 1):
            for c in range(self.config.cols):
                tasks.append(((r, c), (r + 1, c), 'vertical'))
        
        # 并行处理
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=self.config.num_threads) as executor:
            future_to_task = {
                executor.submit(self._calculate_single_offset, task, gray_images): task 
                for task in tasks
            }
            
            valid_count = 0
            with tqdm(total=len(tasks), desc="🔄 相位相关", unit="对") as pbar:
                for future in as_completed(future_to_task):
                    result = future.result()
                    if result:
                        self.pairwise_offsets[result['pair']] = result['offset']
                        valid_count += 1
                    pbar.update(1)
        
        calc_time = time.time() - start_time
        print(f"   ✅ 计算完成：{valid_count}/{len(tasks)} 有效匹配，耗时 {calc_time:.2f}s")
    
    def _calculate_single_offset(self, pair_info, gray_images):
        """计算单个图像对的偏移量"""
        (r1, c1), (r2, c2), direction = pair_info
        
        if (r1, c1) not in gray_images or (r2, c2) not in gray_images:
            return None
        
        img1, img2 = gray_images[(r1, c1)], gray_images[(r2, c2)]
        
        try:
            roi1, roi2 = self._extract_overlap_roi(img1, img2, direction)
            dx, dy, confidence = self._phase_correlation(roi1, roi2)
            
            # 调整到全图坐标系
            if direction == 'horizontal':
                actual_dx = img1.shape[1] - roi1.shape[1] + dx
                actual_dy = dy
            else:
                actual_dx = dx
                actual_dy = img1.shape[0] - roi1.shape[0] + dy
            
            return {
                'pair': ((r1, c1), (r2, c2)),
                'offset': (actual_dx, actual_dy, confidence),
                'direction': direction
            }
        except Exception:
            return None
    
    def _extract_overlap_roi(self, img1, img2, direction):
        """提取重叠区域"""
        h1, w1 = img1.shape
        
        if direction == 'horizontal':
            overlap_width = max(int(w1 * self.config.overlap_ratio), 200)
            roi1 = img1[:, -overlap_width:]
            roi2 = img2[:, :overlap_width]
        else:
            overlap_height = max(int(h1 * self.config.overlap_ratio), 200)
            roi1 = img1[-overlap_height:, :]
            roi2 = img2[:overlap_height, :]
        
        return roi1, roi2
    
    def _phase_correlation(self, img1, img2):
        """相位相关核心算法"""
        def apply_hanning_window(img):
            h, w = img.shape
            hann_h = np.hanning(h).reshape(-1, 1)
            hann_w = np.hanning(w).reshape(1, -1)
            return img.astype(np.float32) * (hann_h * hann_w)
        
        # 确保相同尺寸
        if img1.shape != img2.shape:
            h = min(img1.shape[0], img2.shape[0])
            w = min(img1.shape[1], img2.shape[1])
            img1, img2 = img1[:h, :w], img2[:h, :w]
        
        # 应用窗函数
        img1_windowed = apply_hanning_window(img1)
        img2_windowed = apply_hanning_window(img2)
        
        # FFT计算
        f1 = np.fft.fft2(img1_windowed)
        f2 = np.fft.fft2(img2_windowed)
        
        # 相位相关
        cross_power = f1 * np.conj(f2)
        cross_power_abs = np.abs(cross_power) + 1e-15
        cross_power_norm = cross_power / cross_power_abs
        
        correlation = np.real(np.fft.ifft2(cross_power_norm))
        correlation = np.fft.fftshift(correlation)
        
        # 亚像素峰值检测
        h, w = correlation.shape
        peak_y, peak_x = np.unravel_index(np.argmax(correlation), correlation.shape)
        
        # 抛物线拟合亚像素精化
        if 0 < peak_x < w-1 and 0 < peak_y < h-1:
            # X方向
            c1, c2, c3 = correlation[peak_y, peak_x-1:peak_x+2]
            dx = 0.5 * (c1 - c3) / (c1 + c3 - 2*c2) if abs(c1 + c3 - 2*c2) > 1e-10 else 0
            
            # Y方向
            c1, c2, c3 = correlation[peak_y-1:peak_y+2, peak_x]
            dy = 0.5 * (c1 - c3) / (c1 + c3 - 2*c2) if abs(c1 + c3 - 2*c2) > 1e-10 else 0
        else:
            dx = dy = 0
        
        # 转换到图像坐标系
        center_x, center_y = w // 2, h // 2
        offset_x = peak_x + dx - center_x
        offset_y = peak_y + dy - center_y
        
        # 计算置信度
        max_val = correlation[peak_y, peak_x]
        mask = np.ones_like(correlation, dtype=bool)
        mask[max(0, peak_y-2):peak_y+3, max(0, peak_x-2):peak_x+3] = False
        bg_mean = correlation[mask].mean()
        bg_std = correlation[mask].std()
        confidence = min(1.0, (max_val - bg_mean) / (bg_std + 1e-10) / 20.0)
        
        return offset_x, offset_y, confidence


class GlobalOptimizer:
    """ImageJ风格全局优化器 - 采用Gauss-Seidel迭代和异常值剔除"""
    
    def __init__(self, config):
        self.config = config
        self.final_positions = {}
        
        # 优化参数
        self.max_iterations = 200
        self.max_allowed_error = 2.0
        self.plateau_width = 10
        self.convergence_threshold = 0.0001
        self.relative_threshold = 2.0  # 用于异常值检测
        self.absolute_threshold = 5.0
        
    def optimize_positions(self, pairwise_offsets, color_images):
        """全局优化所有图像位置 - 采用ImageJ算法"""
        print(f"\n🎯 第三阶段：ImageJ风格全局优化")
        
        max_iterations = 5  # 外层迭代次数（异常值剔除）
        
        for iteration in range(max_iterations):
            print(f"   🔄 第 {iteration + 1} 轮优化")
            
            # 构建瓦片图结构
            tiles = self._build_tile_graph(pairwise_offsets, color_images)
            
            if not tiles:
                print("   ❌ 没有足够的瓦片连接，使用简单布局")
                self._fallback_layout(color_images)
                return
            
            # 执行Gauss-Seidel优化
            success, avg_error, max_error, worst_match = self._gauss_seidel_optimize(tiles)
            
            print(f"   📊 平均误差: {avg_error:.3f}px, 最大误差: {max_error:.3f}px")
            
            # 检查是否需要剔除异常值
            if self._should_remove_outlier(avg_error, max_error):
                if worst_match:
                    pair_info = worst_match['pair_info']
                    print(f"   🗑️  剔除异常匹配: {pair_info} (误差: {worst_match['error']:.3f}px)")
                    
                    # 从pairwise_offsets中移除该匹配
                    if pair_info in pairwise_offsets:
                        del pairwise_offsets[pair_info]
                    reverse_pair = (pair_info[1], pair_info[0])
                    if reverse_pair in pairwise_offsets:
                        del pairwise_offsets[reverse_pair]
                    
                    continue  # 重新优化
                else:
                    break  # 没有找到异常值，优化完成
            else:
                print("   ✅ 收敛！无需剔除异常值")
                break
        
        # 提取最终位置
        self._extract_final_positions(tiles)
        
        print(f"   ✅ 全局优化完成：{len(self.final_positions)} 个瓦片")
    
    def _build_tile_graph(self, pairwise_offsets, color_images):
        """构建瓦片图结构 - ImageJ风格"""
        tiles = {}
        
        # 创建所有瓦片
        for (r, c) in color_images.keys():
            tiles[(r, c)] = {
                'position': [0.0, 0.0],  # [x, y]
                'matches': [],           # 匹配列表
                'connected_tiles': set() # 连接的瓦片
            }
        
        # 添加匹配关系
        for pair, (dx, dy, confidence) in pairwise_offsets.items():
            tile1_pos, tile2_pos = pair
            
            if tile1_pos in tiles and tile2_pos in tiles and confidence >= self.config.confidence_threshold:
                # 为tile1添加匹配（指向tile2）
                match1 = {
                    'target_tile': tile2_pos,
                    'offset': [dx, dy],
                    'weight': confidence,
                    'pair_info': pair
                }
                tiles[tile1_pos]['matches'].append(match1)
                tiles[tile1_pos]['connected_tiles'].add(tile2_pos)
                
                # 为tile2添加匹配（指向tile1）
                match2 = {
                    'target_tile': tile1_pos,
                    'offset': [-dx, -dy],
                    'weight': confidence,
                    'pair_info': pair
                }
                tiles[tile2_pos]['matches'].append(match2)
                tiles[tile2_pos]['connected_tiles'].add(tile1_pos)
        
        # 过滤掉没有连接的瓦片
        connected_tiles = {pos: tile for pos, tile in tiles.items() if tile['matches']}
        
        print(f"   🔗 构建图结构：{len(connected_tiles)} 个连接瓦片, {len(pairwise_offsets)} 个匹配")
        
        return connected_tiles
    
    def _gauss_seidel_optimize(self, tiles):
        """Gauss-Seidel迭代优化 - ImageJ风格"""
        
        # 选择固定瓦片（参考点）
        fixed_tile = self._select_fixed_tile(tiles)
        
        # 初始化位置
        self._initialize_positions(tiles, fixed_tile)
        
        # 误差记录
        error_history = []
        
        start_time = time.time()
        
        for iteration in range(self.max_iterations):
            # 更新所有非固定瓦片的位置
            for tile_pos, tile_data in tiles.items():
                if tile_pos == fixed_tile:
                    continue  # 跳过固定瓦片
                
                # 基于所有匹配计算新位置
                new_x, new_y = self._calculate_optimal_position(tile_pos, tiles)
                tile_data['position'] = [new_x, new_y]
            
            # 计算当前误差
            avg_error, max_error, worst_match = self._calculate_errors(tiles)
            error_history.append(avg_error)
            
            # 检查收敛
            if self._check_convergence(error_history, avg_error):
                opt_time = time.time() - start_time
                print(f"   ✅ 收敛于第 {iteration + 1} 次迭代，耗时 {opt_time:.3f}s")
                return True, avg_error, max_error, worst_match
        
        opt_time = time.time() - start_time
        print(f"   ⚠️  达到最大迭代次数 {self.max_iterations}，耗时 {opt_time:.3f}s")
        
        return False, error_history[-1] if error_history else float('inf'), max_error, worst_match
    
    def _select_fixed_tile(self, tiles):
        """选择最适合作为固定点的瓦片"""
        if (0, 0) in tiles and tiles[(0, 0)]['matches']:
            return (0, 0)
        
        # 选择连接度最高的瓦片
        best_tile = None
        max_connections = 0
        
        for tile_pos, tile_data in tiles.items():
            connections = len(tile_data['matches'])
            if connections > max_connections:
                max_connections = connections
                best_tile = tile_pos
        
        return best_tile
    
    def _initialize_positions(self, tiles, fixed_tile):
        """初始化瓦片位置"""
        # 固定瓦片位置为原点
        tiles[fixed_tile]['position'] = [0.0, 0.0]
        
        # 广度优先搜索初始化其他瓦片
        visited = {fixed_tile}
        queue = [fixed_tile]
        
        while queue:
            current_tile = queue.pop(0)
            current_pos = tiles[current_tile]['position']
            
            for match in tiles[current_tile]['matches']:
                target_tile = match['target_tile']
                
                if target_tile in visited:
                    continue
                
                # 基于当前瓦片位置和偏移计算目标瓦片位置
                offset = match['offset']
                new_x = current_pos[0] + offset[0]
                new_y = current_pos[1] + offset[1]
                tiles[target_tile]['position'] = [new_x, new_y]
                
                visited.add(target_tile)
                queue.append(target_tile)
    
    def _calculate_optimal_position(self, tile_pos, tiles):
        """计算瓦片的最优位置 - 基于加权最小二乘"""
        tile_data = tiles[tile_pos]
        
        sum_wx = 0.0
        sum_wy = 0.0
        sum_w = 0.0
        
        for match in tile_data['matches']:
            target_tile = match['target_tile']
            target_pos = tiles[target_tile]['position']
            offset = match['offset']
            weight = match['weight']
            
            # 基于目标位置和偏移计算期望位置
            expected_x = target_pos[0] - offset[0]
            expected_y = target_pos[1] - offset[1]
            
            sum_wx += weight * expected_x
            sum_wy += weight * expected_y
            sum_w += weight
        
        if sum_w > 0:
            return sum_wx / sum_w, sum_wy / sum_w
        else:
            return tile_data['position']  # 保持原位置
    
    def _calculate_errors(self, tiles):
        """计算全局误差"""
        total_error = 0.0
        total_weight = 0.0
        max_error = 0.0
        worst_match = None
        
        for tile_pos, tile_data in tiles.items():
            tile_position = tile_data['position']
            
            for match in tile_data['matches']:
                target_tile = match['target_tile']
                target_position = tiles[target_tile]['position']
                offset = match['offset']
                weight = match['weight']
                
                # 计算预期位置和实际位置的差异
                expected_target_x = tile_position[0] + offset[0]
                expected_target_y = tile_position[1] + offset[1]
                
                error_x = expected_target_x - target_position[0]
                error_y = expected_target_y - target_position[1]
                error = np.sqrt(error_x**2 + error_y**2)
                
                total_error += weight * error
                total_weight += weight
                
                if error > max_error:
                    max_error = error
                    worst_match = {
                        'pair_info': match['pair_info'],
                        'error': error,
                        'match': match
                    }
        
        avg_error = total_error / total_weight if total_weight > 0 else 0.0
        return avg_error, max_error, worst_match
    
    def _check_convergence(self, error_history, current_error):
        """检查优化收敛"""
        if len(error_history) < self.plateau_width:
            return False
        
        if current_error > self.max_allowed_error:
            return False
        
        # 检查斜率是否足够小
        recent_errors = error_history[-self.plateau_width:]
        if len(recent_errors) >= 2:
            slope = abs((recent_errors[-1] - recent_errors[0]) / len(recent_errors))
            if slope <= self.convergence_threshold:
                return True
        
        return False
    
    def _should_remove_outlier(self, avg_error, max_error):
        """判断是否应该移除异常值"""
        return (avg_error * self.relative_threshold < max_error and max_error > 0.95) or avg_error > self.absolute_threshold
    
    def _fallback_layout(self, color_images):
        """回退到简单网格布局"""
        self.final_positions = {}
        
        # 假设图像尺寸
        sample_img = next(iter(color_images.values()))
        img_height, img_width = sample_img.shape[:2]
        
        for r in range(self.config.rows):
            for c in range(self.config.cols):
                if (r, c) in color_images:
                    x = c * img_width * (1 - self.config.overlap_ratio)
                    y = r * img_height * (1 - self.config.overlap_ratio)
                    self.final_positions[(r, c)] = (x, y)
    
    def _extract_final_positions(self, tiles):
        """提取最终位置"""
        self.final_positions = {}
        
        for tile_pos, tile_data in tiles.items():
            x, y = tile_data['position']
            self.final_positions[tile_pos] = (x, y)


class ImageBlender:
    """图像融合器 - 处理最终图像融合"""
    
    def __init__(self, config):
        self.config = config
    
    def blend_images(self, color_images, final_positions, output_path):
        """融合所有图像"""
        print(f"\n🎨 第四阶段：智能图像融合")
        
        canvas_width, canvas_height, offset_x, offset_y = self._calculate_canvas_size(
            color_images, final_positions
        )
        
        print(f"   📏 画布尺寸：{canvas_width} × {canvas_height}")
        
        if self.config.use_gpu and torch.cuda.is_available():
            print(f"   🎮 使用GPU加速融合")
            return self._blend_gpu(
                color_images, final_positions, 
                canvas_width, canvas_height, offset_x, offset_y, output_path
            )
        else:
            print(f"   💾 使用CPU内存高效融合")
            return self._blend_cpu(
                color_images, final_positions,
                canvas_width, canvas_height, offset_x, offset_y, output_path
            )
    
    def _calculate_canvas_size(self, color_images, final_positions):
        """计算画布尺寸"""
        sample_img = next(iter(color_images.values()))
        img_height, img_width = sample_img.shape[:2]
        
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        for (r, c), (x, y) in final_positions.items():
            if (r, c) in color_images:
                corners = [
                    (x, y), (x + img_width, y),
                    (x, y + img_height), (x + img_width, y + img_height)
                ]
                for corner_x, corner_y in corners:
                    min_x, max_x = min(min_x, corner_x), max(max_x, corner_x)
                    min_y, max_y = min(min_y, corner_y), max(max_y, corner_y)
        
        canvas_width = int(np.ceil(max_x - min_x))
        canvas_height = int(np.ceil(max_y - min_y))
        offset_x, offset_y = -min_x, -min_y
        
        return canvas_width, canvas_height, offset_x, offset_y
    
    def _create_weight_map(self, img_shape):
        """创建权重图"""
        h, w = img_shape[:2]
        y_indices, x_indices = np.ogrid[:h, :w]
        
        dist_to_edge = np.minimum(
            np.minimum(y_indices, h - 1 - y_indices),
            np.minimum(x_indices, w - 1 - x_indices)
        )
        
        feather_pixels = min(self.config.max_feather_pixels, min(h, w) // 10)
        feather_pixels = max(feather_pixels, 5)
        
        sigma = feather_pixels / 3.0
        weight_map = np.exp(-((feather_pixels - dist_to_edge) ** 2) / (2 * sigma ** 2))
        weight_map = np.clip(weight_map, 0.1, 1.0)
        
        inner_mask = dist_to_edge >= feather_pixels
        weight_map[inner_mask] = 1.0
        
        return weight_map.astype(np.float32)
    
    def _blend_gpu(self, color_images, final_positions, canvas_width, canvas_height, offset_x, offset_y, output_path):
        """GPU加速融合"""
        device = torch.device('cuda')
        chunk_size = self.config.gpu_chunk_size
        result = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)
        
        y_chunks = (canvas_height + chunk_size - 1) // chunk_size
        x_chunks = (canvas_width + chunk_size - 1) // chunk_size
        
        with tqdm(total=y_chunks * x_chunks, desc="🧩 GPU分块融合") as pbar:
            for y_start in range(0, canvas_height, chunk_size):
                y_end = min(y_start + chunk_size, canvas_height)
                
                for x_start in range(0, canvas_width, chunk_size):
                    x_end = min(x_start + chunk_size, canvas_width)
                    
                    torch.cuda.empty_cache()
                    
                    chunk_h, chunk_w = y_end - y_start, x_end - x_start
                    chunk_canvas = torch.zeros((chunk_h, chunk_w, 3), dtype=torch.float32, device=device)
                    chunk_weight = torch.zeros((chunk_h, chunk_w), dtype=torch.float32, device=device)
                    
                    # 处理重叠图像
                    for (r, c), (x, y) in final_positions.items():
                        if (r, c) not in color_images:
                            continue
                        
                        img = color_images[(r, c)]
                        img_h, img_w = img.shape[:2]
                        img_x = int(x + offset_x)
                        img_y = int(y + offset_y)
                        
                        # 检查重叠
                        if (img_x + img_w <= x_start or img_x >= x_end or 
                            img_y + img_h <= y_start or img_y >= y_end):
                            continue
                        
                        # 计算重叠区域
                        overlap_x1 = max(img_x, x_start)
                        overlap_y1 = max(img_y, y_start)
                        overlap_x2 = min(img_x + img_w, x_end)
                        overlap_y2 = min(img_y + img_h, y_end)
                        
                        if overlap_x2 <= overlap_x1 or overlap_y2 <= overlap_y1:
                            continue
                        
                        # 提取区域
                        img_x1, img_y1 = overlap_x1 - img_x, overlap_y1 - img_y
                        img_x2, img_y2 = overlap_x2 - img_x, overlap_y2 - img_y
                        chunk_x1, chunk_y1 = overlap_x1 - x_start, overlap_y1 - y_start
                        chunk_x2, chunk_y2 = overlap_x2 - x_start, overlap_y2 - y_start
                        
                        img_roi = img[img_y1:img_y2, img_x1:img_x2].astype(np.float32)
                        weight_roi = self._create_weight_map(img.shape)[img_y1:img_y2, img_x1:img_x2]
                        
                        # 转换到GPU
                        img_tensor = torch.from_numpy(img_roi).to(device)
                        weight_tensor = torch.from_numpy(weight_roi).to(device)
                        
                        # 融合
                        for ch in range(3):
                            chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2, ch] += (
                                img_tensor[:, :, ch] * weight_tensor
                            )
                        chunk_weight[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += weight_tensor
                    
                    # 归一化
                    chunk_weight = torch.clamp(chunk_weight, min=1e-10)
                    chunk_result = torch.zeros_like(chunk_canvas)
                    for ch in range(3):
                        chunk_result[:, :, ch] = chunk_canvas[:, :, ch] / chunk_weight
                    
                    chunk_result = torch.clamp(chunk_result, 0, 255).byte().cpu().numpy()
                    result[y_start:y_end, x_start:x_end] = chunk_result
                    
                    pbar.update(1)
        
        cv2.imwrite(output_path, result, [cv2.IMWRITE_JPEG_QUALITY, 85])
        return result
    
    def _blend_cpu(self, color_images, final_positions, canvas_width, canvas_height, offset_x, offset_y, output_path):
        """CPU内存高效融合"""
        canvas = np.zeros((canvas_height, canvas_width, 3), dtype=np.float32)
        weight_canvas = np.zeros((canvas_height, canvas_width), dtype=np.float32)
        
        with tqdm(total=len(final_positions), desc="🎨 CPU融合", unit="张") as pbar:
            for (r, c), (x, y) in final_positions.items():
                if (r, c) not in color_images:
                    pbar.update(1)
                    continue
                
                img = color_images[(r, c)]
                h, w = img.shape[:2]
                
                x_adj = int(x + offset_x)
                y_adj = int(y + offset_y)
                weight_map = self._create_weight_map(img.shape)
                
                # 计算覆盖区域
                x_start, y_start = max(0, x_adj), max(0, y_adj)
                x_end, y_end = min(x_adj + w, canvas_width), min(y_adj + h, canvas_height)
                
                if x_start < x_end and y_start < y_end:
                    # 计算有效区域
                    img_x_start, img_y_start = max(0, -x_adj), max(0, -y_adj)
                    img_x_end = img_x_start + (x_end - x_start)
                    img_y_end = img_y_start + (y_end - y_start)
                    
                    img_region = img[img_y_start:img_y_end, img_x_start:img_x_end]
                    weight_region = weight_map[img_y_start:img_y_end, img_x_start:img_x_end]
                    
                    # 累积到画布
                    for c_idx in range(3):
                        canvas[y_start:y_end, x_start:x_end, c_idx] += (
                            img_region[:, :, c_idx].astype(np.float32) * weight_region
                        )
                    weight_canvas[y_start:y_end, x_start:x_end] += weight_region
                
                pbar.update(1)
        
        # 归一化
        mask = weight_canvas > 0
        result = np.zeros_like(canvas, dtype=np.uint8)
        for c_idx in range(3):
            result[:, :, c_idx][mask] = (canvas[:, :, c_idx][mask] / weight_canvas[mask]).astype(np.uint8)
        
        cv2.imwrite(output_path, result, [cv2.IMWRITE_JPEG_QUALITY, 85])
        return result


class GridStitcher:
    """主拼接器类 - 协调所有组件"""
    
    def __init__(self, config):
        self.config = config
        self.loader = ImageLoader(config)
        self.matcher = PhaseCorrelationMatcher(config)
        self.optimizer = GlobalOptimizer(config)
        self.blender = ImageBlender(config)
    
    def stitch(self, output_path=None):
        """执行完整拼接流程"""
        if output_path is None:
            output_path = f"{self.config.image_dir}_stitched_advanced.jpg"
        
        total_start = time.time()
        
        print("🚀 " + "="*60)
        print("🚀 高性能图像拼接 - 亚像素精度配准")
        print("🚀 " + "="*60)
        
        try:
            # 四个主要阶段
            self.loader.load_all_images()
            
            self.matcher.calculate_all_offsets(self.loader.gray_images)
            
            self.optimizer.optimize_positions(
                self.matcher.pairwise_offsets, 
                self.loader.color_images
            )
            
            result_image = self.blender.blend_images(
                self.loader.color_images,
                self.optimizer.final_positions,
                output_path
            )
            
            # 保存配置文件
            self._save_configuration(output_path)
            
            # 显示结果
            total_time = time.time() - total_start
            file_size_mb = os.path.getsize(output_path) / (1024 * 1024)
            
            print("\n🏁 " + "="*60)
            print("🏁 拼接完成!")
            print("🏁 " + "="*60)
            print(f"📊 图像尺寸: {result_image.shape}")
            print(f"🎯 处理图像: {len(self.optimizer.final_positions)}张")
            print(f"💾 文件大小: {file_size_mb:.1f}MB")
            print(f"⏱️  总耗时: {total_time:.2f}s")
            print(f"📸 结果保存至: {output_path}")
            print("🏁 " + "="*60)
            
            return self.optimizer.final_positions, result_image
            
        except Exception as e:
            print(f"❌ 拼接失败: {str(e)}")
            raise
    
    def _save_configuration(self, output_path):
        """保存配置文件"""
        config_file = "TileConfiguration.registered-python.txt"
        
        with open(config_file, 'w') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n\n")
            f.write("# Define the image coordinates (subpixel precision registration by Python)\n")
            
            for r in range(self.config.rows):
                for c in range(self.config.cols):
                    if (r, c) in self.optimizer.final_positions:
                        x, y = self.optimizer.final_positions[(r, c)]
                        f.write(f"r{r:03d}_c{c:03d}.jpg; ; ({x:.6f}, {y:.6f})\n")
        
        print(f"📄 配准文件已保存: {config_file}")


# ========================= 简化的调用接口 =========================

def stitch_from_directory(image_dir, rows, cols, overlap_ratio=0.1, use_gpu=True, output_path=None):
    """
    从图像目录创建拼接
    
    参数:
        image_dir: 图像文件夹路径
        rows: 网格行数
        cols: 网格列数
        overlap_ratio: 重叠度 (0.1 = 10%)
        use_gpu: 是否使用GPU加速
        output_path: 输出文件路径
    
    返回:
        (final_positions, result_image)
    """
    config = StitchingConfig.from_directory(image_dir, rows, cols, overlap_ratio)
    config.use_gpu = use_gpu
    
    stitcher = GridStitcher(config)
    return stitcher.stitch(output_path)


def stitch_from_config_file(image_dir, tile_config_path, use_gpu=True, output_path=None):
    """
    从TileConfiguration.txt文件创建拼接
    
    参数:
        image_dir: 图像文件夹路径
        tile_config_path: TileConfiguration.txt文件路径
        use_gpu: 是否使用GPU加速
        output_path: 输出文件路径
    
    返回:
        (final_positions, result_image)
    """
    config = StitchingConfig.from_tile_config(image_dir, tile_config_path)
    config.use_gpu = use_gpu
    
    stitcher = GridStitcher(config)
    return stitcher.stitch(output_path)


def main():
    """简化的主函数"""
    
    # ====================== 使用示例 ======================
    
     #方式1: 手动指定参数
    #positions, result = stitch_from_directory(
    #     image_dir="test02",
     #    rows=6, cols=16,
    #     overlap_ratio=0.1,
    #     use_gpu=True
    #)
    
    # 方式2: 使用配置文件 (推荐)
    positions, result = stitch_from_config_file(
        image_dir="Image_SX89",
        tile_config_path="Image_SX89/TileConfiguration.txt",
        use_gpu=True
    )
    
    # ===================================================
    
    print(f"\n✅ 拼接完成！处理了 {len(positions)} 张图像")


if __name__ == "__main__":
    main() 