import cv2
import numpy as np
import os
import re
import time
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import torch
from typing import Dict, Tuple, Optional
from numba import jit, cuda
from scipy import fft as scipy_fft
from scipy import optimize as scipy_optimize
from scipy import stats as scipy_stats
import numba

os.environ['NUMBA_CACHE_DIR'] = './numba_cache'  # 设置缓存目录

class StitchingConfig:
    """拼接配置类 - 统一管理所有参数"""
    
    def __init__(self):
        # 基本参数
        self.image_dir = None
        self.rows = 3
        self.cols = 3
        self.overlap_ratio = 0.1
        self.num_threads = multiprocessing.cpu_count()
        
        # 输入格式
        self.tile_config_path = None
        self.image_naming_format = "r_c"  # "r_c" 或 "s_sequential"
        self.file_extension = ".jpg"      # 支持".jpg"、".tif"、".tiff"等
        
        # GPU配置
        self.use_gpu = torch.cuda.is_available()
        self.gpu_memory_limit_gb = 4.0
        self.gpu_chunk_size = 12000
        
        # 融合参数
        self.max_feather_pixels = 50
        self.memory_limit_gb = 30
        
        # 优化参数 - ImageJ风格
        self.confidence_threshold = 0.3
        self.relative_error_threshold = 2.0  
        self.absolute_error_threshold = 5.0
        
        # Numba优化选项
        self.use_numba = True
        self.numba_parallel = True  # 启用并行优化
        self.numba_cache = True     # 启用编译缓存
        
        # FFT优化选项
        self.use_scipy_fft = True      # 使用scipy.fft替代numpy.fft
        self.optimize_fft_size = False  # 是否填充到2的幂次（可选）
        self.fft_workers = 16           # FFT并行线程数
    
    @classmethod
    def from_directory(cls, image_dir, rows=None, cols=None, overlap_ratio=0.1):
        """从目录创建配置"""
        config = cls()
        config.image_dir = image_dir
        if rows: config.rows = rows
        if cols: config.cols = cols
        config.overlap_ratio = overlap_ratio
        return config
    
    @classmethod
    def from_tile_config(cls, image_dir, tile_config_path):
        """从TileConfiguration.txt文件创建配置"""
        config = cls()
        config.image_dir = image_dir
        config.tile_config_path = tile_config_path
        return config


class ImageLoader:
    """图像加载器 - 处理图像加载和格式解析"""
    
    def __init__(self, config):
        self.config = config
        self.color_images = {}
        self.gray_images = {}
        
    def load_all_images(self):
        """加载所有图像"""
        print(f"\n📋 第一阶段：并行加载图像")
        print(f"   🧵 使用 {self.config.num_threads} 个线程并行加载")
        
        # 构建文件名映射
        grid_to_filename = self._build_filename_mapping()
        
        def load_single_image(r, c):
            """加载单张图像"""
            filename = self._get_filename(r, c, grid_to_filename)
            if not filename:
                return (r, c), None, None, False
                
            filepath = os.path.join(self.config.image_dir, filename)
            if os.path.exists(filepath):
                color_img = cv2.imread(filepath, cv2.IMREAD_COLOR)
                if color_img is not None:
                    gray_img = cv2.cvtColor(color_img, cv2.COLOR_BGR2GRAY)
                    return (r, c), color_img, gray_img, True
            return (r, c), None, None, False
        
        # 并行加载
        start_time = time.time()
        tasks = [(r, c) for r in range(self.config.rows) for c in range(self.config.cols)]
        
        with ThreadPoolExecutor(max_workers=self.config.num_threads) as executor:
            future_to_pos = {executor.submit(load_single_image, r, c): (r, c) for r, c in tasks}
            
            with tqdm(total=len(tasks), desc="🖼️  加载图像", unit="张") as pbar:
                for future in as_completed(future_to_pos):
                    (r, c), color_img, gray_img, success = future.result()
                    if success:
                        self.color_images[(r, c)] = color_img
                        self.gray_images[(r, c)] = gray_img
                    pbar.update(1)
        
        load_time = time.time() - start_time
        print(f"   ✅ 加载完成：{len(self.color_images)} 张图像，耗时 {load_time:.2f}s")
        
        if not self.color_images:
            raise ValueError("❌ 未找到任何图像文件！")
    
    def _build_filename_mapping(self):
        """构建文件名映射"""
        if not self.config.tile_config_path:
            return {}
            
        # 解析TileConfiguration.txt
        grid_info = self._parse_tile_configuration()
        self.config.rows = grid_info['rows']
        self.config.cols = grid_info['cols']
        self.config.overlap_ratio = grid_info['overlap_ratio']
        self.config.image_naming_format = grid_info['naming_format']
        
        return grid_info.get('grid_to_filename', {})
    
    def _parse_tile_configuration(self):
        """解析TileConfiguration.txt文件"""
        config_path = self.config.tile_config_path
        print(f"   📋 解析配置文件: {config_path}")
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        positions = {}
        with open(config_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('#') or line.startswith('dim') or not line:
                    continue
                
                match = re.match(r'(\S+)\s*;\s*;\s*\(([-\d.]+),\s*([-\d.]+)\)', line)
                if match:
                    filename = match.group(1)
                    x = float(match.group(2))
                    y = float(match.group(3))
                    positions[filename] = (x, y)
                    
                    # 从第一个文件名中提取扩展名
                    if hasattr(self.config, 'file_extension') and len(positions) == 1:
                        _, ext = os.path.splitext(filename)
                        if ext:
                            self.config.file_extension = ext
        
        return self._analyze_grid_structure(positions)
    
    def _analyze_grid_structure(self, positions):
        """分析网格结构"""
        coords = list(positions.values())
        x_coords = sorted(list(set([coord[0] for coord in coords])))
        y_coords = sorted(list(set([coord[1] for coord in coords])))
        
        rows, cols = len(y_coords), len(x_coords)
        
        # 计算重叠度
        if len(x_coords) > 1:
            x_spacing = x_coords[1] - x_coords[0]
            overlap_ratio = max(0, (2448 - x_spacing) / 2448)  # 假设图像宽度2448
        else:
            overlap_ratio = 0.1
        
        # 检测命名格式
        sample_filename = list(positions.keys())[0]
        naming_format = "s_sequential" if sample_filename.startswith('s_') else "r_c"
        
        # 构建网格到文件名的映射
        grid_to_filename = {}
        if naming_format == "s_sequential":
            sorted_files = sorted(positions.keys(), key=lambda x: int(x.split('_')[1].split('.')[0]))
            for idx, filename in enumerate(sorted_files):
                row = idx // cols
                col = (idx % cols) if row % 2 == 0 else (cols - 1 - (idx % cols))
                grid_to_filename[(row, col)] = filename
        
        print(f"   ✅ 检测到 {rows}×{cols} 网格，重叠度 {overlap_ratio*100:.1f}%")
        
        return {
            'rows': rows,
            'cols': cols,
            'overlap_ratio': overlap_ratio,
            'naming_format': naming_format,
            'grid_to_filename': grid_to_filename
        }
    
    def _get_filename(self, r, c, grid_to_filename):
        """获取指定位置的文件名"""
        if grid_to_filename and (r, c) in grid_to_filename:
            return grid_to_filename[(r, c)]
        else:
            # 默认返回JPG格式，但也可以根据配置返回其他格式
            return f"r{r:03d}_c{c:03d}{self.config.file_extension}"


@jit(nopython=True, cache=True)
def _apply_hanning_window_numba(img: np.ndarray) -> np.ndarray:
    """Numba加速的汉宁窗函数"""
    h, w = img.shape
    result = np.zeros_like(img, dtype=np.float32)
    
    # 预计算汉宁窗
    hann_h = np.zeros(h, dtype=np.float32)
    hann_w = np.zeros(w, dtype=np.float32)
    
    for i in range(h):
        hann_h[i] = 0.5 * (1 - np.cos(2 * np.pi * i / (h - 1)))
    for j in range(w):
        hann_w[j] = 0.5 * (1 - np.cos(2 * np.pi * j / (w - 1)))
    
    # 应用窗函数
    for i in range(h):
        for j in range(w):
            result[i, j] = img[i, j] * hann_h[i] * hann_w[j]
    
    return result

@jit(nopython=True, cache=True)
def _subpixel_peak_detection_numba(correlation: np.ndarray) -> tuple[float, float, float]:
    """Numba加速的亚像素峰值检测"""
    h, w = correlation.shape
    
    # 找到峰值位置
    max_val = -np.inf
    peak_y = peak_x = 0
    
    for i in range(h):
        for j in range(w):
            if correlation[i, j] > max_val:
                max_val = correlation[i, j]
                peak_y, peak_x = i, j
    
    # 亚像素精化
    dx = dy = 0.0
    if 0 < peak_x < w-1 and 0 < peak_y < h-1:
        # X方向抛物线拟合
        c1 = correlation[peak_y, peak_x-1]
        c2 = correlation[peak_y, peak_x]
        c3 = correlation[peak_y, peak_x+1]
        denom = c1 + c3 - 2*c2
        if abs(denom) > 1e-10:
            dx = 0.5 * (c1 - c3) / denom
        
        # Y方向抛物线拟合
        c1 = correlation[peak_y-1, peak_x]
        c2 = correlation[peak_y, peak_x]
        c3 = correlation[peak_y+1, peak_x]
        denom = c1 + c3 - 2*c2
        if abs(denom) > 1e-10:
            dy = 0.5 * (c1 - c3) / denom
    
    # 计算置信度
    bg_sum = 0.0
    bg_count = 0
    for i in range(h):
        for j in range(w):
            if not (peak_y-2 <= i <= peak_y+2 and peak_x-2 <= j <= peak_x+2):
                bg_sum += correlation[i, j]
                bg_count += 1
    
    bg_mean = bg_sum / bg_count if bg_count > 0 else 0.0
    
    # 计算标准差
    bg_var = 0.0
    for i in range(h):
        for j in range(w):
            if not (peak_y-2 <= i <= peak_y+2 and peak_x-2 <= j <= peak_x+2):
                diff = correlation[i, j] - bg_mean
                bg_var += diff * diff
    
    bg_std = np.sqrt(bg_var / bg_count) if bg_count > 0 else 1.0
    confidence = min(1.0, (max_val - bg_mean) / (bg_std + 1e-10) / 20.0)
    
    return float(peak_x + dx), float(peak_y + dy), confidence


class PhaseCorrelationMatcher:
    """相位相关匹配器 - 处理图像配准"""
    
    def __init__(self, config: StitchingConfig):
        self.config = config
        self.pairwise_offsets: Dict[Tuple[Tuple[int, int], Tuple[int, int]], Tuple[float, float, float]] = {}
        
        # 可选：预规划FFT以获得最佳性能（适用于固定尺寸图像）
        self._fft_planner_cache = {}
    
    def _get_optimal_fft_size(self, size):
        """获取最优FFT尺寸 - 2的幂次通常更快"""
        return int(2 ** np.ceil(np.log2(size)))
    
    def _phase_correlation_optimized(self, img1: np.ndarray, img2: np.ndarray) -> Tuple[float, float, float]:
        """进一步优化的相位相关算法"""
        
        # 确保相同尺寸
        if img1.shape != img2.shape:
            h = min(img1.shape[0], img2.shape[0])
            w = min(img1.shape[1], img2.shape[1])
            img1, img2 = img1[:h, :w], img2[:h, :w]
        
        h, w = img1.shape
        
        # 可选：填充到最优FFT尺寸（2的幂次）
        if self.config.optimize_fft_size:
            opt_h = self._get_optimal_fft_size(h)
            opt_w = self._get_optimal_fft_size(w)
            
            if opt_h != h or opt_w != w:
                # 用零填充到最优尺寸
                img1_padded = np.zeros((opt_h, opt_w), dtype=np.float32)
                img2_padded = np.zeros((opt_h, opt_w), dtype=np.float32)
                
                img1_padded[:h, :w] = img1.astype(np.float32)
                img2_padded[:h, :w] = img2.astype(np.float32)
                
                img1, img2 = img1_padded, img2_padded
        else:
            img1 = img1.astype(np.float32)
            img2 = img2.astype(np.float32)
        
        # 使用Numba加速的窗函数
        img1_windowed = _apply_hanning_window_numba(img1)
        img2_windowed = _apply_hanning_window_numba(img2)
        
        # 使用scipy.fft - 可以指定workers参数进行多线程FFT
        workers = min(4, multiprocessing.cpu_count())  # 限制FFT线程数
        
        f1 = scipy_fft.fft2(img1_windowed, workers=workers)
        f2 = scipy_fft.fft2(img2_windowed, workers=workers)
        
        # 相位相关
        f1_array = np.asarray(f1)
        f2_array = np.asarray(f2)
        cross_power = f1_array * np.conj(f2_array)  # type: ignore
        cross_power_abs = np.abs(cross_power) + 1e-15
        cross_power_norm = cross_power / cross_power_abs
        
        # 逆FFT
        correlation = np.real(scipy_fft.ifft2(cross_power_norm, workers=workers))  # type: ignore
        correlation = scipy_fft.fftshift(correlation)
        
        # 如果进行了填充，需要裁剪回原始尺寸
        if correlation.shape != (h, w):
            start_h = (correlation.shape[0] - h) // 2
            start_w = (correlation.shape[1] - w) // 2
            correlation = correlation[start_h:start_h+h, start_w:start_w+w]
        
        # 使用Numba加速的峰值检测
        peak_x, peak_y, confidence = _subpixel_peak_detection_numba(correlation)
        
        # 转换到图像坐标系
        center_x, center_y = w // 2, h // 2
        offset_x = peak_x - center_x
        offset_y = peak_y - center_y
        
        return offset_x, offset_y, confidence
    
    def calculate_all_offsets(self, gray_images):
        """计算所有相邻图像的偏移量"""
        print(f"\n🔍 第二阶段：并行计算相位相关偏移")
        
        # 准备所有图像对
        tasks = []
        for r in range(self.config.rows):
            for c in range(self.config.cols - 1):
                tasks.append(((r, c), (r, c + 1), 'horizontal'))
        for r in range(self.config.rows - 1):
            for c in range(self.config.cols):
                tasks.append(((r, c), (r + 1, c), 'vertical'))
        
        # 并行处理
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=self.config.num_threads) as executor:
            future_to_task = {
                executor.submit(self._calculate_single_offset, task, gray_images): task 
                for task in tasks
            }
            
            valid_count = 0
            with tqdm(total=len(tasks), desc="🔄 相位相关", unit="对") as pbar:
                for future in as_completed(future_to_task):
                    result = future.result()
                    if result:
                        self.pairwise_offsets[result['pair']] = result['offset']
                        valid_count += 1
                    pbar.update(1)
        
        calc_time = time.time() - start_time
        print(f"   ✅ 计算完成：{valid_count}/{len(tasks)} 有效匹配，耗时 {calc_time:.2f}s")
    
    def _calculate_single_offset(self, pair_info, gray_images):
        """计算单个图像对的偏移量"""
        (r1, c1), (r2, c2), direction = pair_info
        
        if (r1, c1) not in gray_images or (r2, c2) not in gray_images:
            return None
        
        img1, img2 = gray_images[(r1, c1)], gray_images[(r2, c2)]
        
        try:
            roi1, roi2 = self._extract_overlap_roi(img1, img2, direction)
            dx, dy, confidence = self._phase_correlation_optimized(roi1, roi2)
            
            # 调整到全图坐标系
            if direction == 'horizontal':
                actual_dx = img1.shape[1] - roi1.shape[1] + dx
                actual_dy = dy
            else:
                actual_dx = dx
                actual_dy = img1.shape[0] - roi1.shape[0] + dy
            
            return {
                'pair': ((r1, c1), (r2, c2)),
                'offset': (actual_dx, actual_dy, confidence),
                'direction': direction
            }
        except Exception:
            return None
    
    def _extract_overlap_roi(self, img1: np.ndarray, img2: np.ndarray, direction: str) -> Tuple[np.ndarray, np.ndarray]:
        """提取重叠区域"""
        h1, w1 = img1.shape
        
        if direction == 'horizontal':
            overlap_width = max(int(w1 * self.config.overlap_ratio), 200)
            roi1 = img1[:, -overlap_width:]
            roi2 = img2[:, :overlap_width]
        else:
            overlap_height = max(int(h1 * self.config.overlap_ratio), 200)
            roi1 = img1[-overlap_height:, :]
            roi2 = img2[:overlap_height, :]
        
        return roi1, roi2


@jit(nopython=True, cache=True, parallel=True)
def _create_weight_map_numba_parallel(h: int, w: int, feather_pixels: int) -> np.ndarray:
    """Numba并行加速的权重图创建"""
    weight_map = np.ones((h, w), dtype=np.float32)
    
    sigma = feather_pixels / 3.0
    sigma2 = 2 * sigma * sigma
    
    # 使用Numba并行化
    for i in numba.prange(h):
        for j in range(w):
            # 计算到边缘的距离
            dist_to_edge = min(
                min(i, h - 1 - i),
                min(j, w - 1 - j)
            )
            
            if dist_to_edge < feather_pixels:
                # 高斯衰减
                diff = feather_pixels - dist_to_edge
                weight = np.exp(-(diff * diff) / sigma2)
                weight_map[i, j] = max(0.1, weight)
    
    return weight_map


class ImageBlender:
    """图像融合器 - 处理最终图像融合"""
    
    def __init__(self, config):
        self.config = config
        # 权重图缓存
        self._weight_map_cache = {}
        self._precomputed_weights = {}
    
    def _precompute_weight_maps(self, color_images):
        """多线程预计算所有权重图"""
        print("   🧮 预计算权重图...")
        
        # 找到所有唯一的图像尺寸
        unique_shapes = set()
        for img in color_images.values():
            unique_shapes.add(img.shape[:2])
        
        # 多线程计算权重图
        def compute_single_weight_map(shape):
            h, w = shape
            feather_pixels = min(self.config.max_feather_pixels, min(h, w) // 10)
            feather_pixels = max(feather_pixels, 5)
            return shape, _create_weight_map_numba_parallel(h, w, feather_pixels)
        
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=self.config.num_threads) as executor:
            future_to_shape = {
                executor.submit(compute_single_weight_map, shape): shape 
                for shape in unique_shapes
            }
            
            for future in as_completed(future_to_shape):
                shape, weight_map = future.result()
                self._weight_map_cache[shape] = weight_map
        
        precompute_time = time.time() - start_time
        print(f"   ✅ 权重图预计算完成：{len(unique_shapes)} 种尺寸，耗时 {precompute_time:.3f}s")
    
    def _get_cached_weight_map(self, img_shape: Tuple[int, int]) -> np.ndarray:
        """获取缓存的权重图"""
        shape = img_shape[:2]
        if shape in self._weight_map_cache:
            return self._weight_map_cache[shape]
        
        # 如果缓存中没有，临时计算一个
        h, w = shape
        feather_pixels = min(self.config.max_feather_pixels, min(h, w) // 10)
        feather_pixels = max(feather_pixels, 5)
        weight_map = _create_weight_map_numba_parallel(h, w, feather_pixels)
        self._weight_map_cache[shape] = weight_map
        return weight_map
    
    def blend_images(self, color_images, final_positions, output_path):
        """融合所有图像"""
        print(f"\n🎨 第四阶段：智能图像融合")
        
        # 预计算权重图
        self._precompute_weight_maps(color_images)
        
        canvas_width, canvas_height, offset_x, offset_y = self._calculate_canvas_size(
            color_images, final_positions
        )
        
        print(f"   📏 画布尺寸：{canvas_width} × {canvas_height}")
        
        if self.config.use_gpu and torch.cuda.is_available():
            print(f"   🎮 使用GPU加速融合")
            return self._blend_gpu(
                color_images, final_positions, 
                canvas_width, canvas_height, offset_x, offset_y, output_path
            )
        else:
            print(f"   💾 使用CPU内存高效融合")
            return self._blend_cpu(
                color_images, final_positions,
                canvas_width, canvas_height, offset_x, offset_y, output_path
            )
    
    def _calculate_canvas_size(self, color_images, final_positions):
        """计算画布尺寸"""
        sample_img = next(iter(color_images.values()))
        img_height, img_width = sample_img.shape[:2]
        
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        for (r, c), (x, y) in final_positions.items():
            if (r, c) in color_images:
                corners = [
                    (x, y), (x + img_width, y),
                    (x, y + img_height), (x + img_width, y + img_height)
                ]
                for corner_x, corner_y in corners:
                    min_x, max_x = min(min_x, corner_x), max(max_x, corner_x)
                    min_y, max_y = min(min_y, corner_y), max(max_y, corner_y)
        
        canvas_width = int(np.ceil(max_x - min_x))
        canvas_height = int(np.ceil(max_y - min_y))
        offset_x, offset_y = -min_x, -min_y
        
        return canvas_width, canvas_height, offset_x, offset_y
    
    def _create_weight_map(self, img_shape: Tuple[int, int]) -> np.ndarray:
        """创建权重图 - 使用缓存版本"""
        return self._get_cached_weight_map(img_shape)
    
    def _blend_gpu_optimized(self, 
                   color_images: Dict[Tuple[int, int], np.ndarray],
                   final_positions: Dict[Tuple[int, int], Tuple[float, float]],
                   canvas_width: int, 
                   canvas_height: int, 
                   offset_x: float, 
                   offset_y: float, 
                   output_path: str) -> np.ndarray:
        """高度优化的GPU加速融合"""
        device = torch.device('cuda')
        chunk_size = self.config.gpu_chunk_size
        result = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)
        
        # 预处理：将所有权重图预加载到GPU
        print("   🎮 预加载权重图到GPU...")
        gpu_weight_cache = {}
        for shape in self._weight_map_cache:
            weight_map = self._weight_map_cache[shape]
            gpu_weight_cache[shape] = torch.from_numpy(weight_map).to(device)
        
        y_chunks = (canvas_height + chunk_size - 1) // chunk_size
        x_chunks = (canvas_width + chunk_size - 1) // chunk_size
        
        with tqdm(total=y_chunks * x_chunks, desc="🧩 GPU优化融合") as pbar:
            for y_start in range(0, canvas_height, chunk_size):
                y_end = min(y_start + chunk_size, canvas_height)
                
                for x_start in range(0, canvas_width, chunk_size):
                    x_end = min(x_start + chunk_size, canvas_width)
                    
                    # 只在必要时清理GPU缓存
                    if pbar.n % 3 == 0:  # 每3个chunk清理一次
                        torch.cuda.empty_cache()
                    
                    chunk_h, chunk_w = y_end - y_start, x_end - x_start
                    chunk_canvas = torch.zeros((chunk_h, chunk_w, 3), dtype=torch.float32, device=device)
                    chunk_weight = torch.zeros((chunk_h, chunk_w), dtype=torch.float32, device=device)
                    
                    # 批量处理重叠图像
                    batch_data = []
                    for (r, c), (x, y) in final_positions.items():
                        if (r, c) not in color_images:
                            continue
                        
                        img = color_images[(r, c)]
                        img_h, img_w = img.shape[:2]
                        img_x = int(x + offset_x)
                        img_y = int(y + offset_y)
                        
                        # 检查重叠
                        if (img_x + img_w <= x_start or img_x >= x_end or 
                            img_y + img_h <= y_start or img_y >= y_end):
                            continue
                        
                        # 计算重叠区域
                        overlap_x1 = max(img_x, x_start)
                        overlap_y1 = max(img_y, y_start)
                        overlap_x2 = min(img_x + img_w, x_end)
                        overlap_y2 = min(img_y + img_h, y_end)
                        
                        if overlap_x2 <= overlap_x1 or overlap_y2 <= overlap_y1:
                            continue
                        
                        # 提取区域
                        img_x1, img_y1 = overlap_x1 - img_x, overlap_y1 - img_y
                        img_x2, img_y2 = overlap_x2 - img_x, overlap_y2 - img_y
                        chunk_x1, chunk_y1 = overlap_x1 - x_start, overlap_y1 - y_start
                        chunk_x2, chunk_y2 = overlap_x2 - x_start, overlap_y2 - y_start
                        
                        batch_data.append({
                            'img': img,
                            'img_roi': (img_y1, img_y2, img_x1, img_x2),
                            'chunk_roi': (chunk_y1, chunk_y2, chunk_x1, chunk_x2),
                            'shape': img.shape[:2]
                        })
                    
                    # 批量GPU处理
                    if batch_data:
                        # 一次性传输所有数据到GPU
                        for data in batch_data:
                            img = data['img']
                            img_y1, img_y2, img_x1, img_x2 = data['img_roi']
                            chunk_y1, chunk_y2, chunk_x1, chunk_x2 = data['chunk_roi']
                            shape = data['shape']
                            
                            img_roi = img[img_y1:img_y2, img_x1:img_x2].astype(np.float32)
                            
                            # 使用预加载的GPU权重图
                            if shape in gpu_weight_cache:
                                weight_tensor = gpu_weight_cache[shape][img_y1:img_y2, img_x1:img_x2]
                            else:
                                # 后备方案
                                weight_roi = self._get_cached_weight_map(img.shape)[img_y1:img_y2, img_x1:img_x2]
                                weight_tensor = torch.from_numpy(weight_roi).to(device)
                            
                            # 转换到GPU
                            img_tensor = torch.from_numpy(img_roi).to(device)
                            
                            # 向量化融合操作
                            weighted_img = img_tensor * weight_tensor.unsqueeze(-1)
                            chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += weighted_img
                            chunk_weight[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += weight_tensor
                    
                    # 归一化和转换
                    chunk_weight = torch.clamp(chunk_weight, min=1e-10)
                    chunk_result = chunk_canvas / chunk_weight.unsqueeze(-1)
                    chunk_result = torch.clamp(chunk_result, 0, 255).byte().cpu().numpy()
                    result[y_start:y_end, x_start:x_end] = chunk_result
                    
                    pbar.update(1)
        
        cv2.imwrite(output_path, result, [cv2.IMWRITE_JPEG_QUALITY, 85])
        return result
    
    def _blend_gpu(self, 
                   color_images: Dict[Tuple[int, int], np.ndarray],
                   final_positions: Dict[Tuple[int, int], Tuple[float, float]],
                   canvas_width: int, 
                   canvas_height: int, 
                   offset_x: float, 
                   offset_y: float, 
                   output_path: str) -> np.ndarray:
        """GPU加速融合 - 智能选择优化版本"""
        
        # 根据数据量选择最优算法
        total_images = len(final_positions)
        canvas_size = canvas_width * canvas_height
        
        if total_images > 50 and canvas_size > 10_000_000:  # 大数据集使用优化版本
            return self._blend_gpu_optimized(
                color_images, final_positions, canvas_width, canvas_height, 
                offset_x, offset_y, output_path
            )
        else:
            return self._blend_gpu_standard(
                color_images, final_positions, canvas_width, canvas_height, 
                offset_x, offset_y, output_path
            )
    
    def _blend_gpu_standard(self, 
                   color_images: Dict[Tuple[int, int], np.ndarray],
                   final_positions: Dict[Tuple[int, int], Tuple[float, float]],
                   canvas_width: int, 
                   canvas_height: int, 
                   offset_x: float, 
                   offset_y: float, 
                   output_path: str) -> np.ndarray:
        """标准GPU加速融合 - 使用预计算的权重图"""
        device = torch.device('cuda')
        chunk_size = self.config.gpu_chunk_size
        result = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)
        
        # 预处理：将所有权重图预加载到GPU
        print("   🎮 预加载权重图到GPU...")
        gpu_weight_cache = {}
        for shape in self._weight_map_cache:
            weight_map = self._weight_map_cache[shape]
            gpu_weight_cache[shape] = torch.from_numpy(weight_map).to(device)
        
        y_chunks = (canvas_height + chunk_size - 1) // chunk_size
        x_chunks = (canvas_width + chunk_size - 1) // chunk_size
        
        with tqdm(total=y_chunks * x_chunks, desc="🧩 GPU标准融合") as pbar:
            for y_start in range(0, canvas_height, chunk_size):
                y_end = min(y_start + chunk_size, canvas_height)
                
                for x_start in range(0, canvas_width, chunk_size):
                    x_end = min(x_start + chunk_size, canvas_width)
                    
                    # 只在必要时清理GPU缓存
                    if pbar.n % 3 == 0:  # 每3个chunk清理一次
                        torch.cuda.empty_cache()
                    
                    chunk_h, chunk_w = y_end - y_start, x_end - x_start
                    chunk_canvas = torch.zeros((chunk_h, chunk_w, 3), dtype=torch.float32, device=device)
                    chunk_weight = torch.zeros((chunk_h, chunk_w), dtype=torch.float32, device=device)
                    
                    # 批量处理重叠图像
                    batch_data = []
                    for (r, c), (x, y) in final_positions.items():
                        if (r, c) not in color_images:
                            continue
                        
                        img = color_images[(r, c)]
                        img_h, img_w = img.shape[:2]
                        img_x = int(x + offset_x)
                        img_y = int(y + offset_y)
                        
                        # 检查重叠
                        if (img_x + img_w <= x_start or img_x >= x_end or 
                            img_y + img_h <= y_start or img_y >= y_end):
                            continue
                        
                        # 计算重叠区域
                        overlap_x1 = max(img_x, x_start)
                        overlap_y1 = max(img_y, y_start)
                        overlap_x2 = min(img_x + img_w, x_end)
                        overlap_y2 = min(img_y + img_h, y_end)
                        
                        if overlap_x2 <= overlap_x1 or overlap_y2 <= overlap_y1:
                            continue
                        
                        # 提取区域
                        img_x1, img_y1 = overlap_x1 - img_x, overlap_y1 - img_y
                        img_x2, img_y2 = overlap_x2 - img_x, overlap_y2 - img_y
                        chunk_x1, chunk_y1 = overlap_x1 - x_start, overlap_y1 - y_start
                        chunk_x2, chunk_y2 = overlap_x2 - x_start, overlap_y2 - y_start
                        
                        batch_data.append({
                            'img': img,
                            'img_roi': (img_y1, img_y2, img_x1, img_x2),
                            'chunk_roi': (chunk_y1, chunk_y2, chunk_x1, chunk_x2),
                            'shape': img.shape[:2]
                        })
                    
                    # 批量GPU处理
                    if batch_data:
                        # 一次性传输所有数据到GPU
                        for data in batch_data:
                            img = data['img']
                            img_y1, img_y2, img_x1, img_x2 = data['img_roi']
                            chunk_y1, chunk_y2, chunk_x1, chunk_x2 = data['chunk_roi']
                            shape = data['shape']
                            
                            img_roi = img[img_y1:img_y2, img_x1:img_x2].astype(np.float32)
                            
                            # 使用预加载的GPU权重图
                            if shape in gpu_weight_cache:
                                weight_tensor = gpu_weight_cache[shape][img_y1:img_y2, img_x1:img_x2]
                            else:
                                # 后备方案
                                weight_roi = self._get_cached_weight_map(img.shape)[img_y1:img_y2, img_x1:img_x2]
                                weight_tensor = torch.from_numpy(weight_roi).to(device)
                            
                            # 转换到GPU
                            img_tensor = torch.from_numpy(img_roi).to(device)
                            
                            # 向量化融合操作
                            weighted_img = img_tensor * weight_tensor.unsqueeze(-1)
                            chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += weighted_img
                            chunk_weight[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += weight_tensor
                    
                    # 归一化和转换
                    chunk_weight = torch.clamp(chunk_weight, min=1e-10)
                    chunk_result = chunk_canvas / chunk_weight.unsqueeze(-1)
                    chunk_result = torch.clamp(chunk_result, 0, 255).byte().cpu().numpy()
                    result[y_start:y_end, x_start:x_end] = chunk_result
                    
                    pbar.update(1)
        
        cv2.imwrite(output_path, result, [cv2.IMWRITE_JPEG_QUALITY, 85])
        return result
    
    def _blend_cpu(self, color_images, final_positions, canvas_width, canvas_height, offset_x, offset_y, output_path):
        """CPU内存高效融合 - 多线程版本"""
        canvas = np.zeros((canvas_height, canvas_width, 3), dtype=np.float32)
        weight_canvas = np.zeros((canvas_height, canvas_width), dtype=np.float32)
        
        # 多线程处理图像融合
        def process_single_image(item):
            (r, c), (x, y) = item
            if (r, c) not in color_images:
                return None
            
            img = color_images[(r, c)]
            h, w = img.shape[:2]
            
            x_adj = int(x + offset_x)
            y_adj = int(y + offset_y)
            weight_map = self._get_cached_weight_map(img.shape)
            
            # 计算覆盖区域
            x_start, y_start = max(0, x_adj), max(0, y_adj)
            x_end, y_end = min(x_adj + w, canvas_width), min(y_adj + h, canvas_height)
            
            if x_start < x_end and y_start < y_end:
                # 计算有效区域
                img_x_start, img_y_start = max(0, -x_adj), max(0, -y_adj)
                img_x_end = img_x_start + (x_end - x_start)
                img_y_end = img_y_start + (y_end - y_start)
                
                img_region = img[img_y_start:img_y_end, img_x_start:img_x_end]
                weight_region = weight_map[img_y_start:img_y_end, img_x_start:img_x_end]
                
                return {
                    'region': (y_start, y_end, x_start, x_end),
                    'img_data': img_region.astype(np.float32),
                    'weight_data': weight_region
                }
            return None
        
        # 并行处理所有图像
        print("   🎨 多线程CPU融合...")
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=self.config.num_threads) as executor:
            future_to_pos = {
                executor.submit(process_single_image, item): item 
                for item in final_positions.items()
            }
            
            # 使用锁保护canvas更新（虽然numpy操作通常是线程安全的）
            import threading
            canvas_lock = threading.Lock()
            
            with tqdm(total=len(final_positions), desc="🧩 CPU并行融合", unit="张") as pbar:
                for future in as_completed(future_to_pos):
                    result = future.result()
                    if result:
                        region = result['region']
                        img_data = result['img_data']
                        weight_data = result['weight_data']
                        
                        y_start, y_end, x_start, x_end = region
                        
                        # 线程安全的canvas更新
                        with canvas_lock:
                            for c_idx in range(3):
                                canvas[y_start:y_end, x_start:x_end, c_idx] += (
                                    img_data[:, :, c_idx] * weight_data
                                )
                            weight_canvas[y_start:y_end, x_start:x_end] += weight_data
                    
                    pbar.update(1)
        
        blend_time = time.time() - start_time
        print(f"   ✅ CPU融合完成，耗时 {blend_time:.3f}s")
        
        # 归一化
        print("   🔢 最终归一化...")
        mask = weight_canvas > 0
        result = np.zeros_like(canvas, dtype=np.uint8)
        for c_idx in range(3):
            result[:, :, c_idx][mask] = (canvas[:, :, c_idx][mask] / weight_canvas[mask]).astype(np.uint8)
        
        cv2.imwrite(output_path, result, [cv2.IMWRITE_JPEG_QUALITY, 85])
        return result


class GlobalOptimizer:
    """稳健全局优化器 - 采用稳健损失函数的全局最小二乘求解"""
    
    def __init__(self, config: StitchingConfig):
        self.config = config
        self.final_positions: Dict[Tuple[int, int], Tuple[float, float]] = {}
        
        # 基本优化参数
        self.max_iterations = 500
        self.convergence_threshold = 1e-6
        
        # 稳健损失函数参数
        self.use_robust_loss = True
        self.robust_loss_type = 'huber'     # 'huber', 'cauchy', 'geman_mcclure'
        self.huber_delta = 2.0              # Huber损失的阈值
        self.cauchy_c = 1.0                 # Cauchy损失的缩放参数
        
        # 权重策略
        self.confidence_weighting = True    # 是否使用置信度加权
        self.adaptive_weighting = True      # 是否使用自适应权重
        
        # 收敛和调试
        self.debug_mode = False
    
    def _robust_loss(self, residuals: np.ndarray, loss_type: Optional[str] = None) -> np.ndarray:
        """
        计算稳健损失函数的权重
        
        参数:
            residuals: 残差数组 (shape: [N,])
            loss_type: 损失函数类型
        
        返回:
            weights: 权重数组 (shape: [N,])
        """
        if loss_type is None:
            loss_type = self.robust_loss_type
        
        abs_residuals = np.abs(residuals)
        
        if loss_type == 'huber':
            # Huber损失: L(r) = 0.5*r^2 if |r| <= δ, δ*(|r| - 0.5*δ) otherwise
            # 权重: w(r) = 1 if |r| <= δ, δ/|r| otherwise
            weights = np.ones_like(abs_residuals)
            large_residual_mask = abs_residuals > self.huber_delta
            weights[large_residual_mask] = self.huber_delta / abs_residuals[large_residual_mask]
            
        elif loss_type == 'cauchy':
            # Cauchy损失: L(r) = c^2/2 * log(1 + (r/c)^2)
            # 权重: w(r) = 1 / (1 + (r/c)^2)
            weights = 1.0 / (1.0 + (abs_residuals / self.cauchy_c) ** 2)
            
        elif loss_type == 'geman_mcclure':
            # Geman-McClure损失: L(r) = r^2 / (2*(1 + r^2))
            # 权重: w(r) = 1 / (1 + r^2)^2
            weights = 1.0 / (1.0 + abs_residuals ** 2) ** 2
            
        elif loss_type == 'none' or loss_type == 'l2':
            # 标准L2损失，无稳健性
            weights = np.ones_like(abs_residuals)
            
        else:
            raise ValueError(f"未知的损失函数类型: {loss_type}")
        
        return weights
    
    def _estimate_residual_scale(self, residuals: np.ndarray) -> float:
        """
        估计残差的鲁棒尺度参数 (使用MAD - Median Absolute Deviation)
        """
        mad = np.median(np.abs(residuals - np.median(residuals)))
        # 转换为标准差等价值 (假设正态分布)
        scale = mad * 1.4826
        return max(float(scale), 1e-6)  # 避免除零
    
    def _adaptive_robust_parameters(self, residuals: np.ndarray):
        """
        根据当前残差自适应调整稳健损失函数参数
        """
        if not self.adaptive_weighting:
            return
        
        scale = self._estimate_residual_scale(residuals)
        
        if self.robust_loss_type == 'huber':
            # 设置Huber阈值为1.5倍的鲁棒尺度
            self.huber_delta = 1.5 * scale
        elif self.robust_loss_type == 'cauchy':
            # 设置Cauchy参数为鲁棒尺度
            self.cauchy_c = scale
        
        if self.debug_mode:
            print(f"   📊 自适应参数更新: scale={scale:.3f}, "
                  f"huber_δ={self.huber_delta:.3f}, cauchy_c={self.cauchy_c:.3f}")
    
    def optimize_positions(self, 
                         pairwise_offsets: Dict[Tuple[Tuple[int, int], Tuple[int, int]], Tuple[float, float, float]], 
                         color_images: Dict[Tuple[int, int], np.ndarray]) -> None:
        """稳健全局优化所有图像位置 - 采用稳健损失函数"""
        print(f"\n🎯 第三阶段：稳健全局优化")
        print(f"   🛡️  使用 {self.robust_loss_type.upper()} 稳健损失函数，自动处理异常值")
        
        # 构建瓦片图结构
        tiles = self._build_tile_graph(pairwise_offsets, color_images)
        
        if not tiles:
            print("   ❌ 没有足够的瓦片连接，使用简单布局")
            self._fallback_layout(color_images)
            return
        
        # 执行稳健优化
        success, final_error = self._robust_optimize(tiles)
        
        if success:
            print(f"   ✅ 稳健优化收敛！最终误差: {final_error:.3f}px")
        else:
            print(f"   ⚠️  达到最大迭代次数，最终误差: {final_error:.3f}px")
        
        # 提取最终位置
        self._extract_final_positions(tiles)
        
        print(f"   ✅ 全局优化完成：{len(self.final_positions)} 个瓦片")
    
    def _robust_optimize(self, tiles) -> Tuple[bool, float]:
        """
        稳健优化核心算法 - 使用IRLS (Iteratively Reweighted Least Squares)
        """
        print(f"   🔗 开始稳健优化：{len(tiles)} 个瓦片")
        
        # 选择固定瓦片（参考点）
        fixed_tile = self._select_fixed_tile(tiles)
        
        # 初始化位置
        self._initialize_positions(tiles, fixed_tile)
        
        start_time = time.time()
        prev_error = float('inf')
        
        for iteration in range(self.max_iterations):
            # 1. 计算当前残差
            residuals = self._compute_residuals(tiles)
            
            if len(residuals) == 0:
                print("   ⚠️  没有有效的残差，使用简单布局")
                return False, float('inf')
            
            # 2. 计算当前误差
            current_error = np.sqrt(np.mean(residuals ** 2))  # RMSE
            
            # 3. 自适应调整稳健参数
            if iteration == 0 or (iteration + 1) % 10 == 0:
                self._adaptive_robust_parameters(residuals)
            
            # 4. 计算稳健权重
            if self.use_robust_loss:
                robust_weights = self._robust_loss(residuals)
            else:
                robust_weights = np.ones_like(residuals)
            
            # 5. 加权最小二乘更新位置
            self._weighted_position_update(tiles, robust_weights, fixed_tile)
            
            # 6. 检查收敛
            error_change = abs(prev_error - current_error)
            
            if self.debug_mode and (iteration < 10 or (iteration + 1) % 50 == 0):
                num_downweighted = np.sum(robust_weights < 0.5)
                print(f"   🔄 迭代 {iteration + 1}: RMSE={current_error:.4f}, "
                      f"变化={error_change:.6f}, 降权点={num_downweighted}")
            
            if error_change < self.convergence_threshold and iteration > 10:
                opt_time = time.time() - start_time
                print(f"   ✅ 稳健优化收敛于第 {iteration + 1} 次迭代，耗时 {opt_time:.3f}s")
                return True, current_error
            
            prev_error = current_error
        
        opt_time = time.time() - start_time
        print(f"   ⚠️  达到最大迭代次数 {self.max_iterations}，耗时 {opt_time:.3f}s")
        return False, current_error
    
    def _compute_residuals(self, tiles) -> np.ndarray:
        """
        计算所有匹配的残差
        """
        residuals = []
        
        for tile_pos, tile_data in tiles.items():
            tile_position = np.array(tile_data['position'])
            
            for match in tile_data['matches']:
                target_tile = match['target_tile']
                target_position = np.array(tiles[target_tile]['position'])
                offset = np.array(match['offset'])
                
                # 预期目标位置
                expected_target = tile_position + offset
                
                # 残差向量 (2D)
                residual_vector = expected_target - target_position
                
                # 计算标量残差 (L2范数)
                residual = np.linalg.norm(residual_vector)
                residuals.append(residual)
        
        return np.array(residuals)
    
    def _weighted_position_update(self, tiles, weights: np.ndarray, fixed_tile):
        """
        使用加权最小二乘更新所有瓦片位置
        """
        # 收集所有匹配约束
        equations = []
        weight_idx = 0
        
        for tile_pos, tile_data in tiles.items():
            tile_position = tile_data['position']
            
            for match in tile_data['matches']:
                target_tile = match['target_tile']
                offset = match['offset']
                confidence = match['weight']
                
                # 组合权重：稳健权重 × 原始置信度权重
                if self.confidence_weighting:
                    combined_weight = weights[weight_idx] * confidence
                else:
                    combined_weight = weights[weight_idx]
                
                equations.append({
                    'tile1': tile_pos,
                    'tile2': target_tile,
                    'offset': offset,
                    'weight': combined_weight
                })
                
                weight_idx += 1
        
        # 为每个非固定瓦片计算新位置
        for tile_pos in tiles.keys():
            if tile_pos == fixed_tile:
                continue
            
            # 收集与该瓦片相关的所有约束
            tile_equations = []
            for eq in equations:
                if eq['tile1'] == tile_pos or eq['tile2'] == tile_pos:
                    tile_equations.append(eq)
            
            if not tile_equations:
                continue
            
            # 加权最小二乘求解新位置
            new_position = self._solve_weighted_position(tile_pos, tile_equations, tiles)
            tiles[tile_pos]['position'] = new_position
    
    def _solve_weighted_position(self, target_tile, equations, tiles) -> list:
        """
        为单个瓦片求解加权最小二乘位置
        """
        sum_weighted_pos = np.array([0.0, 0.0])
        sum_weights = 0.0
        
        for eq in equations:
            weight = eq['weight']
            offset = np.array(eq['offset'])
            
            if eq['tile1'] == target_tile:
                # target_tile + offset = tile2_pos
                # => target_tile = tile2_pos - offset
                other_tile = eq['tile2']
                other_pos = np.array(tiles[other_tile]['position'])
                expected_pos = other_pos - offset
            else:
                # tile1 + offset = target_tile
                # => target_tile = tile1 + offset
                other_tile = eq['tile1']
                other_pos = np.array(tiles[other_tile]['position'])
                expected_pos = other_pos + offset
            
            sum_weighted_pos += weight * expected_pos
            sum_weights += weight
        
        if sum_weights > 1e-10:
            return (sum_weighted_pos / sum_weights).tolist()
        else:
            return tiles[target_tile]['position']  # 保持原位置
    
    def _build_tile_graph(self, pairwise_offsets, color_images):
        """构建瓦片图结构 - ImageJ风格"""
        tiles = {}
        
        # 创建所有瓦片
        for (r, c) in color_images.keys():
            tiles[(r, c)] = {
                'position': [0.0, 0.0],  # [x, y]
                'matches': [],           # 匹配列表
                'connected_tiles': set() # 连接的瓦片
            }
        
        # 添加匹配关系
        for pair, (dx, dy, confidence) in pairwise_offsets.items():
            tile1_pos, tile2_pos = pair
            
            if tile1_pos in tiles and tile2_pos in tiles and confidence >= self.config.confidence_threshold:
                # 为tile1添加匹配（指向tile2）
                match1 = {
                    'target_tile': tile2_pos,
                    'offset': [dx, dy],
                    'weight': confidence,
                    'pair_info': pair
                }
                tiles[tile1_pos]['matches'].append(match1)
                tiles[tile1_pos]['connected_tiles'].add(tile2_pos)
                
                # 为tile2添加匹配（指向tile1）
                match2 = {
                    'target_tile': tile1_pos,
                    'offset': [-dx, -dy],
                    'weight': confidence,
                    'pair_info': pair
                }
                tiles[tile2_pos]['matches'].append(match2)
                tiles[tile2_pos]['connected_tiles'].add(tile1_pos)
        
        # 过滤掉没有连接的瓦片
        connected_tiles = {pos: tile for pos, tile in tiles.items() if tile['matches']}
        
        print(f"   🔗 构建图结构：{len(connected_tiles)} 个连接瓦片, {len(pairwise_offsets)} 个匹配")
        
        return connected_tiles
    
    def _select_fixed_tile(self, tiles):
        """选择最适合作为固定点的瓦片"""
        if (0, 0) in tiles and tiles[(0, 0)]['matches']:
            return (0, 0)
        
        # 选择连接度最高的瓦片
        best_tile = None
        max_connections = 0
        
        for tile_pos, tile_data in tiles.items():
            connections = len(tile_data['matches'])
            if connections > max_connections:
                max_connections = connections
                best_tile = tile_pos
        
        return best_tile
    
    def _initialize_positions(self, tiles, fixed_tile):
        """初始化瓦片位置"""
        # 固定瓦片位置为原点
        tiles[fixed_tile]['position'] = [0.0, 0.0]
        
        # 广度优先搜索初始化其他瓦片
        visited = {fixed_tile}
        queue = [fixed_tile]
        
        while queue:
            current_tile = queue.pop(0)
            current_pos = tiles[current_tile]['position']
            
            for match in tiles[current_tile]['matches']:
                target_tile = match['target_tile']
                
                if target_tile in visited:
                    continue
                
                # 基于当前瓦片位置和偏移计算目标瓦片位置
                offset = match['offset']
                new_x = current_pos[0] + offset[0]
                new_y = current_pos[1] + offset[1]
                tiles[target_tile]['position'] = [new_x, new_y]
                
                visited.add(target_tile)
                queue.append(target_tile)
    
    def _fallback_layout(self, color_images):
        """回退到简单网格布局"""
        self.final_positions = {}
        
        # 假设图像尺寸
        sample_img = next(iter(color_images.values()))
        img_height, img_width = sample_img.shape[:2]
        
        for r in range(self.config.rows):
            for c in range(self.config.cols):
                if (r, c) in color_images:
                    x = c * img_width * (1 - self.config.overlap_ratio)
                    y = r * img_height * (1 - self.config.overlap_ratio)
                    self.final_positions[(r, c)] = (x, y)
    
    def _extract_final_positions(self, tiles):
        """提取最终位置"""
        self.final_positions = {}
        
        # 提取优化后的位置
        for tile_pos, tile_data in tiles.items():
            x, y = tile_data['position']
            self.final_positions[tile_pos] = (x, y)


class GridStitcher:
    """主拼接器类 - 协调所有组件"""
    
    def __init__(self, config):
        self.config = config
        self.loader = ImageLoader(config)
        self.matcher = PhaseCorrelationMatcher(config)
        self.optimizer = GlobalOptimizer(config)
        self.blender = ImageBlender(config)
    
    def stitch(self, output_path=None):
        """执行完整拼接流程"""
        if output_path is None:
            # 从目录路径中提取基本名称，例如 "Image_55"
            base_name = os.path.basename(os.path.normpath(self.config.image_dir))
            # 使用与输入相同的文件扩展名，默认为jpg
            output_ext = '.jpg'  # 默认输出扩展名
            if hasattr(self.config, 'file_extension') and self.config.file_extension != '.jpg':
                output_ext = '.jpg'  # 始终输出为jpg格式，因为这是一个拼接后的大图像
            output_filename = f"{base_name}{output_ext}"
            output_path = os.path.join(self.config.image_dir, output_filename)
        
        total_start = time.time()
        
        print("🚀 " + "="*60)
        print("🚀 高性能图像拼接 - 亚像素精度配准")
        print("🚀 " + "="*60)
        
        try:
            # 四个主要阶段
            self.loader.load_all_images()
            
            self.matcher.calculate_all_offsets(self.loader.gray_images)
            
            self.optimizer.optimize_positions(
                self.matcher.pairwise_offsets, 
                self.loader.color_images
            )
            
            result_image = self.blender.blend_images(
                self.loader.color_images,
                self.optimizer.final_positions,
                output_path
            )
            
            # 保存配置文件
            self._save_configuration(output_path)
            
            # 显示结果
            total_time = time.time() - total_start
            file_size_mb = os.path.getsize(output_path) / (1024 * 1024)
            
            print("\n🏁 " + "="*60)
            print("🏁 拼接完成!")
            print("🏁 " + "="*60)
            print(f"📊 图像尺寸: {result_image.shape}")
            print(f"🎯 处理图像: {len(self.optimizer.final_positions)}张")
            print(f"💾 文件大小: {file_size_mb:.1f}MB")
            print(f"⏱️  总耗时: {total_time:.2f}s")
            print(f"📸 结果保存至: {output_path}")
            print("🏁 " + "="*60)
            
            return self.optimizer.final_positions, result_image
            
        except Exception as e:
            print(f"❌ 拼接失败: {str(e)}")
            raise
    
    def _save_configuration(self, output_path):
        """保存配置文件"""
        config_file = os.path.join(self.config.image_dir, "TileConfiguration.registered-python.txt")
        
        with open(config_file, 'w') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n\n")
            f.write("# Define the image coordinates (subpixel precision registration by Python)\n")
            
            for r in range(self.config.rows):
                for c in range(self.config.cols):
                    if (r, c) in self.optimizer.final_positions:
                        x, y = self.optimizer.final_positions[(r, c)]
                        f.write(f"r{r:03d}_c{c:03d}.jpg; ; ({x:.6f}, {y:.6f})\n")
        
        print(f"📄 配准文件已保存: {config_file}")


# ========================= 简化的调用接口 =========================

def stitch_from_directory(image_dir, rows, cols, overlap_ratio=0.1, use_gpu=True, 
                         robust_loss='huber', output_path=None):
    """
    从图像目录创建拼接（支持稳健优化）
    
    参数:
        image_dir: 图像文件夹路径
        rows: 网格行数
        cols: 网格列数
        overlap_ratio: 重叠度 (0.1 = 10%)
        use_gpu: 是否使用GPU加速
        robust_loss: 稳健损失函数类型 ('huber', 'cauchy', 'geman_mcclure', 'none')
        output_path: 输出文件路径
    
    返回:
        (final_positions, result_image)
    """
    config = StitchingConfig.from_directory(image_dir, rows, cols, overlap_ratio)
    config.use_gpu = use_gpu
    
    stitcher = GridStitcher(config)
    
    # 配置稳健优化
    stitcher.optimizer.robust_loss_type = robust_loss
    stitcher.optimizer.use_robust_loss = (robust_loss != 'none')
    stitcher.optimizer.adaptive_weighting = True
    stitcher.optimizer.confidence_weighting = True
    
    return stitcher.stitch(output_path)


def stitch_from_config_file(image_dir, tile_config_path, use_gpu=True, 
                           robust_loss='huber', output_path=None):
    """
    从TileConfiguration.txt文件创建拼接（支持稳健优化）
    
    参数:
        image_dir: 图像文件夹路径
        tile_config_path: TileConfiguration.txt文件路径
        use_gpu: 是否使用GPU加速
        robust_loss: 稳健损失函数类型 ('huber', 'cauchy', 'geman_mcclure', 'none')
        output_path: 输出文件路径
    
    返回:
        (final_positions, result_image)
    """
    config = StitchingConfig.from_tile_config(image_dir, tile_config_path)
    config.use_gpu = use_gpu
    
    stitcher = GridStitcher(config)
    
    # 配置稳健优化
    stitcher.optimizer.robust_loss_type = robust_loss
    stitcher.optimizer.use_robust_loss = (robust_loss != 'none')
    stitcher.optimizer.adaptive_weighting = True
    stitcher.optimizer.confidence_weighting = True
    
    return stitcher.stitch(output_path)


def warm_up_numba():
    """预热Numba编译器"""
    print("🔥 预热Numba编译器...")
    dummy_img = np.random.randint(0, 255, (100, 100), dtype=np.uint8).astype(np.float32)
    _apply_hanning_window_numba(dummy_img)
    _create_weight_map_numba_parallel(100, 100, 10)
    print("✅ Numba预热完成")


def main():
    warm_up_numba()
    
    # ====================== 稳健优化模式配置 ======================
    image_file = "D:/images/zhouyi-tsmz"
    
    # 使用配置文件
    config = StitchingConfig.from_tile_config(
        image_dir=image_file,
        tile_config_path=image_file+"/TileConfiguration.txt"
    )
    
    config.confidence_threshold = 0.3
    config.use_gpu = True
    
    stitcher = GridStitcher(config)
    
    # 配置稳健优化器参数
    stitcher.optimizer.robust_loss_type = 'huber'      # 'huber', 'cauchy', 'geman_mcclure'
    stitcher.optimizer.use_robust_loss = True          # 启用稳健损失函数
    stitcher.optimizer.adaptive_weighting = True       # 启用自适应权重
    stitcher.optimizer.confidence_weighting = True     # 启用置信度权重
    stitcher.optimizer.debug_mode = False              # 启用调试信息
    
    positions, result = stitcher.stitch()
    # ===================================================
    
    print(f"\n✅ 拼接完成！处理了 {len(positions)} 张图像")


if __name__ == "__main__":
    main() 