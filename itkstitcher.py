import SimpleITK as sitk
import numpy as np
from pathlib import Path
from typing import Dict, Tuple, Optional

class ImageStitcher:
    def __init__(self, image_folder: str, grid_rows: int = 3, grid_cols: int = 3, 
                 overlap_ratio: float = 0.10, feather_width: int = 50):
        """
        初始化图像拼接器
        
        Args:
            image_folder: 图像文件夹路径
            grid_rows: 网格行数
            grid_cols: 网格列数
            overlap_ratio: 重叠比例，默认10%
            feather_width: 羽化宽度，默认50像素
        """
        self.image_folder = Path(image_folder)
        self.grid_rows = grid_rows
        self.grid_cols = grid_cols
        self.images: Dict[Tuple[int, int], sitk.Image] = {}
        self.gray_images: Dict[Tuple[int, int], sitk.Image] = {}
        self.registered_images = {}
        self.overlap_ratio = overlap_ratio
        self.feather_width = feather_width
        
        # 验证参数
        if grid_rows <= 0 or grid_cols <= 0:
            raise ValueError(f"网格尺寸必须为正数: {grid_rows}x{grid_cols}")
        if not 0 < overlap_ratio < 1:
            raise ValueError(f"overlap_ratio必须在0和1之间，当前值: {overlap_ratio}")
        if feather_width < 0:
            raise ValueError(f"feather_width必须为非负数，当前值: {feather_width}")
        
        print(f"初始化拼接器: {grid_rows}行 x {grid_cols}列")
        
    def load_images(self) -> bool:
        """
        加载网格图像
        
        Returns:
            bool: 是否成功加载至少一张图像
        """
        print("加载图像...")
        success_count = 0
        
        for row in range(self.grid_rows):
            for col in range(self.grid_cols):
                filename = f"r{row:03d}_c{col:03d}.jpg"
                filepath = self.image_folder / filename
                
                if filepath.exists():
                    try:
                        # 使用SimpleITK读取图像
                        image = sitk.ReadImage(str(filepath))
                        
                        # 处理彩色图像的类型转换
                        if image.GetNumberOfComponentsPerPixel() > 1:
                            # 多通道图像
                            image_array = sitk.GetArrayFromImage(image)
                            image_array = image_array.astype(np.float32)
                            color_image = sitk.GetImageFromArray(image_array, isVector=True)
                            color_image.CopyInformation(image)
                            
                            # 创建灰度版本用于配准
                            gray_image = sitk.VectorMagnitude(image)
                            gray_image = sitk.Cast(gray_image, sitk.sitkFloat32)
                        else:
                            # 单通道图像
                            color_image = sitk.Cast(image, sitk.sitkFloat32)
                            gray_image = sitk.Cast(image, sitk.sitkFloat32)
                        
                        # 保存图像
                        self.images[(row, col)] = color_image
                        self.gray_images[(row, col)] = gray_image
                        success_count += 1
                        
                        if success_count <= 10 or success_count % 20 == 0:  # 减少输出量
                            print(f"已加载: {filename} (彩色: {image.GetNumberOfComponentsPerPixel()}通道)")
                        
                    except Exception as e:
                        print(f"加载图像 {filename} 时出错: {e}")
                else:
                    if row < 2 and col < 5:  # 只显示前几个缺失文件的警告
                        print(f"警告: 文件不存在 {filename}")
        
        if success_count == 0:
            print("错误: 没有成功加载任何图像")
            return False
            
        print(f"成功加载 {success_count} 张图像 (总共 {self.grid_rows * self.grid_cols} 个位置)")
        return True
    
    def phase_correlation_registration(self, fixed_image, moving_image):
        """
        改进的相位相关法，支持亚像素精度配准
        """
        # 将SimpleITK图像转换为numpy数组
        fixed_array = sitk.GetArrayFromImage(fixed_image)
        moving_array = sitk.GetArrayFromImage(moving_image)
        
        # 确保数组为浮点类型
        fixed_array = fixed_array.astype(np.float64)
        moving_array = moving_array.astype(np.float64)
        
        # 对图像进行预处理（去均值，应用窗函数减少边缘效应）
        fixed_array = fixed_array - np.mean(fixed_array)
        moving_array = moving_array - np.mean(moving_array)
        
        # 应用汉宁窗减少边缘效应
        rows, cols = fixed_array.shape
        win_r = np.hanning(rows).reshape(-1, 1)
        win_c = np.hanning(cols).reshape(1, -1)
        window = win_r * win_c
        
        fixed_array *= window
        moving_array *= window
        
        # FFT相位相关
        f_fixed = np.fft.fft2(fixed_array)
        f_moving = np.fft.fft2(moving_array)
        
        # 计算互功率谱（加入白化处理）
        cross_power_spectrum = f_fixed * np.conj(f_moving)
        magnitude = np.abs(cross_power_spectrum)
        
        # 避免除零，添加小的正则化项
        magnitude = np.maximum(magnitude, np.max(magnitude) * 1e-6)
        cross_power_spectrum /= magnitude
        
        # 逆FFT得到相位相关
        phase_correlation = np.fft.ifft2(cross_power_spectrum)
        phase_correlation = np.abs(phase_correlation)
        
        # 找到峰值位置
        peak_pos = np.unravel_index(np.argmax(phase_correlation), phase_correlation.shape)
        
        # 亚像素精度优化
        shift_x, shift_y = self.subpixel_refinement(phase_correlation, peak_pos)
        
        return shift_x, shift_y
    
    def subpixel_refinement(self, correlation, peak_pos):
        """改进的亚像素精度优化"""
        y, x = peak_pos
        height, width = correlation.shape
        
        # 确保不在边界
        if 1 <= y < height-1 and 1 <= x < width-1:
            # 使用二次函数拟合进行亚像素定位
            c_center = correlation[y, x]
            c_left = correlation[y, x-1]
            c_right = correlation[y, x+1]
            c_up = correlation[y-1, x]
            c_down = correlation[y+1, x]
            
            # X方向亚像素偏移
            denom_x = 2 * (c_left - 2*c_center + c_right)
            if abs(denom_x) > 1e-10:
                dx = (c_left - c_right) / denom_x
            else:
                dx = 0
                
            # Y方向亚像素偏移  
            denom_y = 2 * (c_up - 2*c_center + c_down)
            if abs(denom_y) > 1e-10:
                dy = (c_up - c_down) / denom_y
            else:
                dy = 0
            
            # 限制亚像素偏移范围
            dx = np.clip(dx, -0.5, 0.5)
            dy = np.clip(dy, -0.5, 0.5)
            
            shift_x = x + dx
            shift_y = y + dy
        else:
            shift_x, shift_y = x, y
        
        # 转换为实际位移
        if shift_x > width // 2:
            shift_x -= width
        if shift_y > height // 2:
            shift_y -= height
            
        return shift_x, shift_y
    
    def extract_overlap_roi(self, image1: sitk.Image, image2: sitk.Image, 
                          direction: str = 'horizontal') -> Tuple[sitk.Image, sitk.Image, list, list]:
        """
        提取相邻图像的重叠区域ROI
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            direction: 'horizontal' 或 'vertical'
            
        Returns:
            tuple: (roi1, roi2, roi1_start, roi2_start)
        """
        size = image1.GetSize()  # [width, height]
        
        if direction == 'horizontal':
            # 水平相邻
            overlap_width = max(1, int(size[0] * self.overlap_ratio))
            roi1_start = [size[0] - overlap_width, 0]
            roi1_size = [overlap_width, size[1]]
            roi2_start = [0, 0]
            roi2_size = [overlap_width, size[1]]
        else:  # vertical
            # 垂直相邻
            overlap_height = max(1, int(size[1] * self.overlap_ratio))
            roi1_start = [0, size[1] - overlap_height]
            roi1_size = [size[0], overlap_height]
            roi2_start = [0, 0]
            roi2_size = [size[0], overlap_height]
        
        # 提取ROI
        roi1 = sitk.RegionOfInterest(image1, roi1_size, roi1_start)
        roi2 = sitk.RegionOfInterest(image2, roi2_size, roi2_start)
        
        return roi1, roi2, roi1_start, roi2_start
    
    def phase_correlation_registration_roi(self, fixed_image: sitk.Image, 
                                         moving_image: sitk.Image, 
                                         direction: str = 'horizontal') -> Tuple[float, float]:
        """
        在重叠区域进行相位相关配准
        
        Args:
            fixed_image: 固定图像
            moving_image: 移动图像
            direction: 配准方向
            
        Returns:
            tuple: (shift_x, shift_y)
        """
        try:
            # 提取重叠区域ROI
            fixed_roi, moving_roi, fixed_start, moving_start = self.extract_overlap_roi(
                fixed_image, moving_image, direction)
            
            # 在ROI上进行相位相关
            shift_x, shift_y = self.phase_correlation_registration(fixed_roi, moving_roi)
            
            # 调整偏移量
            if direction == 'horizontal':
                actual_shift_x = fixed_start[0] - moving_start[0] + shift_x
                actual_shift_y = shift_y
            else:  # vertical
                actual_shift_x = shift_x
                actual_shift_y = fixed_start[1] - moving_start[1] + shift_y
            
            # 验证配准质量
            image_size = fixed_image.GetSize()
            if not self.validate_registration_quality(actual_shift_x, actual_shift_y, image_size):
                print(f"配准质量警告: direction={direction}, shift=({actual_shift_x:.1f}, {actual_shift_y:.1f})")
            
            return actual_shift_x, actual_shift_y
            
        except Exception as e:
            print(f"配准过程出错: {e}")
            # 返回默认偏移
            ref_size = fixed_image.GetSize()
            if direction == 'horizontal':
                return ref_size[0] * 0.8, 0  # 80%图像宽度
            else:
                return 0, ref_size[1] * 0.8  # 80%图像高度
    
    def register_images(self):
        """配准所有图像 (使用灰度图像计算偏移量)"""
        print("开始图像配准...")
        
        # 存储累积偏移（相对于参考图像）
        cumulative_shifts = {(0, 0): (0, 0)}
        
        # 先配准第一行（从左到右）
        print("配准第一行...")
        for col in range(1, self.grid_cols):
            if (0, col) in self.gray_images and (0, col-1) in self.gray_images:
                moving = self.gray_images[(0, col)]
                fixed = self.gray_images[(0, col-1)]
                
                # 水平方向配准
                shift_x, shift_y = self.phase_correlation_registration_roi(
                    fixed, moving, direction='horizontal')
                
                if col <= 5 or col % 5 == 0:  # 减少输出
                    print(f"图像 (0,{col}) 相对于 (0,{col-1}) 的偏移: ({shift_x:.2f}, {shift_y:.2f})")
                
                # 累积偏移（相对于参考图像）
                prev_shift = cumulative_shifts[(0, col-1)]
                cumulative_shifts[(0, col)] = (prev_shift[0] + shift_x, prev_shift[1] + shift_y)
        
        # 配准第一列（从上到下）
        print("配准第一列...")
        for row in range(1, self.grid_rows):
            if (row, 0) in self.gray_images and (row-1, 0) in self.gray_images:
                moving = self.gray_images[(row, 0)]
                fixed = self.gray_images[(row-1, 0)]
                
                # 垂直方向配准
                shift_x, shift_y = self.phase_correlation_registration_roi(
                    fixed, moving, direction='vertical')
                print(f"图像 ({row},0) 相对于 ({row-1},0) 的偏移: ({shift_x:.2f}, {shift_y:.2f})")
                
                # 累积偏移（相对于参考图像）
                prev_shift = cumulative_shifts[(row-1, 0)]
                cumulative_shifts[(row, 0)] = (prev_shift[0] + shift_x, prev_shift[1] + shift_y)
        
        # 配准其余图像
        print("配准其余图像...")
        total_remaining = (self.grid_rows - 1) * (self.grid_cols - 1)
        processed = 0
        
        for row in range(1, self.grid_rows):
            for col in range(1, self.grid_cols):
                if (row, col) in self.gray_images:
                    moving = self.gray_images[(row, col)]
                    
                    # 优先选择水平配准（相对于左边图像）
                    if (row, col-1) in cumulative_shifts:
                        fixed_left = self.gray_images[(row, col-1)]
                        shift_x, shift_y = self.phase_correlation_registration_roi(
                            fixed_left, moving, direction='horizontal')
                        prev_shift = cumulative_shifts[(row, col-1)]
                        cumulative_shifts[(row, col)] = (prev_shift[0] + shift_x, prev_shift[1] + shift_y)
                        
                        processed += 1
                        if processed % 20 == 0 or processed <= 10:
                            print(f"已配准 {processed}/{total_remaining} 张图像")
                    
                    # 如果水平配准不可用，使用垂直配准（相对于上面图像）
                    elif (row-1, col) in cumulative_shifts:
                        fixed_up = self.gray_images[(row-1, col)]
                        shift_x, shift_y = self.phase_correlation_registration_roi(
                            fixed_up, moving, direction='vertical')
                        prev_shift = cumulative_shifts[(row-1, col)]
                        cumulative_shifts[(row, col)] = (prev_shift[0] + shift_x, prev_shift[1] + shift_y)
                        
                        processed += 1
                        if processed % 20 == 0 or processed <= 10:
                            print(f"已配准 {processed}/{total_remaining} 张图像")
        
        print(f"配准完成，共配准了 {len(cumulative_shifts)} 张图像")
        return cumulative_shifts
    
    def linear_blend(self, img1, img2, overlap_region):
        """在重叠区域进行线性融合"""
        # 转换为numpy数组
        arr1 = sitk.GetArrayFromImage(img1)
        arr2 = sitk.GetArrayFromImage(img2)
        
        blended = arr1.copy()
        
        # 在重叠区域进行线性融合
        y_start, y_end, x_start, x_end = overlap_region
        
        if y_end > y_start and x_end > x_start:
            overlap_width = x_end - x_start
            overlap_height = y_end - y_start
            
            for i in range(overlap_height):
                for j in range(overlap_width):
                    # 计算权重（从左到右或从上到下渐变）
                    weight = j / overlap_width if overlap_width > 0 else 0.5
                    
                    y_idx = y_start + i
                    x_idx = x_start + j
                    
                    if (y_idx < blended.shape[0] and x_idx < blended.shape[1] and 
                        y_idx < arr2.shape[0] and x_idx < arr2.shape[1]):
                        blended[y_idx, x_idx] = ((1 - weight) * arr1[y_idx, x_idx] + 
                                               weight * arr2[y_idx, x_idx])
        
        # 转换回SimpleITK图像
        result = sitk.GetImageFromArray(blended)
        result.CopyInformation(img1)
        return result
    
    def stitch_images_with_blending(self, cumulative_shifts: Dict[Tuple[int, int], Tuple[float, float]]) -> Optional[sitk.Image]:
        """
        改进的拼接算法：创建画布并进行线性融合
        
        Args:
            cumulative_shifts: 累积偏移字典
            
        Returns:
            Optional[sitk.Image]: 拼接结果或None
        """
        print("开始图像拼接（使用线性融合）...")
        
        try:
            # 获取参考图像信息
            reference = self.images[(0, 0)]
            ref_size = reference.GetSize()
            ref_spacing = reference.GetSpacing()
            num_components = reference.GetNumberOfComponentsPerPixel()
            
            print(f"参考图像尺寸: {ref_size}")
            print(f"图像通道数: {num_components}")
            
            # 计算画布边界
            min_x = min_y = 0
            max_x = max_y = 0
            
            for (row, col), (shift_x, shift_y) in cumulative_shifts.items():
                max_x = max(max_x, shift_x + ref_size[0])
                max_y = max(max_y, shift_y + ref_size[1])
                min_x = min(min_x, shift_x)
                min_y = min(min_y, shift_y)
            
            print(f"边界: min_x={min_x:.1f}, max_x={max_x:.1f}, min_y={min_y:.1f}, max_y={max_y:.1f}")
            
            # 创建画布
            canvas_width = int(max_x - min_x)
            canvas_height = int(max_y - min_y)
            print(f"画布尺寸: {canvas_width} x {canvas_height}")
            
            # 验证画布尺寸合理性
            max_size = 100000  # 对于更大的网格增加限制
            if canvas_width > max_size or canvas_height > max_size:
                raise ValueError(f"画布尺寸过大: {canvas_width}x{canvas_height}, 超过限制{max_size}")
            
            # 创建画布和权重图
            if num_components > 1:
                canvas = np.zeros((canvas_height, canvas_width, num_components), dtype=np.float64)
            else:
                canvas = np.zeros((canvas_height, canvas_width), dtype=np.float64)
            weight_sum = np.zeros((canvas_height, canvas_width), dtype=np.float64)
            
            # 放置图像到画布
            placed_count = 0
            total_images = len(self.images)
            
            for (row, col), image in self.images.items():
                if (row, col) not in cumulative_shifts:
                    continue
                    
                shift_x, shift_y = cumulative_shifts[(row, col)]
                start_x = int(shift_x - min_x)
                start_y = int(shift_y - min_y)
                
                # 获取图像数据
                img_array = sitk.GetArrayFromImage(image).astype(np.float64)
                
                if num_components > 1:
                    img_h, img_w, img_c = img_array.shape
                else:
                    img_h, img_w = img_array.shape
                
                if placed_count % 20 == 0 or placed_count < 10:
                    print(f"放置图像 ({row},{col}) 到位置 ({start_x}, {start_y}) [{placed_count+1}/{total_images}]")
                
                # 计算有效区域
                end_x = min(start_x + img_w, canvas_width)
                end_y = min(start_y + img_h, canvas_height)
                actual_start_x = max(0, start_x)
                actual_start_y = max(0, start_y)
                
                img_start_x = max(0, -start_x)
                img_start_y = max(0, -start_y)
                img_end_x = img_start_x + (end_x - actual_start_x)
                img_end_y = img_start_y + (end_y - actual_start_y)
                
                if end_x > actual_start_x and end_y > actual_start_y:
                    # 创建权重掩码
                    weight_mask = self.create_weight_mask(
                        img_end_y - img_start_y, 
                        img_end_x - img_start_x
                    )
                    
                    # 提取图像区域
                    img_region = img_array[img_start_y:img_end_y, img_start_x:img_end_x]
                    
                    # 累积图像数据
                    if num_components > 1:
                        for c in range(num_components):
                            canvas[actual_start_y:end_y, actual_start_x:end_x, c] += \
                                img_region[:, :, c] * weight_mask
                    else:
                        canvas[actual_start_y:end_y, actual_start_x:end_x] += \
                            img_region * weight_mask
                    
                    # 累积权重
                    weight_sum[actual_start_y:end_y, actual_start_x:end_x] += weight_mask
                    placed_count += 1
            
            if placed_count == 0:
                raise ValueError("没有成功放置任何图像")
            
            # 归一化
            valid_mask = weight_sum > 0
            if num_components > 1:
                for c in range(num_components):
                    canvas[:, :, c][valid_mask] /= weight_sum[valid_mask]
            else:
                canvas[valid_mask] /= weight_sum[valid_mask]
            
            print(f"拼接完成，放置了 {placed_count} 张图像")
            print(f"有效像素比例: {np.sum(valid_mask)/valid_mask.size*100:.1f}%")
            
            # 转换回SimpleITK图像
            result = sitk.GetImageFromArray(canvas, isVector=(num_components > 1))
            result.SetSpacing(ref_spacing)
            result.SetOrigin([0, 0])
            
            return result
            
        except Exception as e:
            print(f"拼接过程出错: {e}")
            return None
    
    def create_weight_mask(self, height: int, width: int) -> np.ndarray:
        """
        创建边缘羽化的权重掩码（修复版本）
        
        Args:
            height: 掩码高度
            width: 掩码宽度
            
        Returns:
            np.ndarray: 权重掩码
        """
        # 创建坐标矩阵
        y_coords, x_coords = np.meshgrid(np.arange(height), np.arange(width), indexing='ij')
        
        # 计算到各边缘的距离
        dist_left = x_coords
        dist_right = width - 1 - x_coords
        dist_top = y_coords
        dist_bottom = height - 1 - y_coords
        
        # 到最近边缘的距离
        dist_to_edge = np.minimum.reduce([dist_left, dist_right, dist_top, dist_bottom])
        
        # 应用羽化
        weight_mask = np.ones_like(dist_to_edge, dtype=np.float64)
        if self.feather_width > 0:
            feather_mask = dist_to_edge < self.feather_width
            weight_mask[feather_mask] = dist_to_edge[feather_mask] / self.feather_width
        
        return weight_mask
    
    def validate_registration_quality(self, shift_x: float, shift_y: float, 
                                    image_size: Tuple[int, int]) -> bool:
        """
        验证配准质量
        
        Args:
            shift_x: X方向偏移
            shift_y: Y方向偏移
            image_size: 图像尺寸 (width, height)
            
        Returns:
            bool: 配准是否合理
        """
        width, height = image_size
        
        # 检查偏移是否过大（超过图像尺寸的80%）
        if abs(shift_x) > width * 0.8 or abs(shift_y) > height * 0.8:
            print(f"警告: 配准偏移过大 ({shift_x:.1f}, {shift_y:.1f})")
            return False
            
        return True
    
    def save_result(self, result: sitk.Image, filename: str = "stitched_result.jpg") -> bool:
        """
        保存拼接结果
        
        Args:
            result: 拼接结果图像
            filename: 输出文件名
            
        Returns:
            bool: 是否保存成功
        """
        try:
            output_path = self.image_folder / filename
            num_components = result.GetNumberOfComponentsPerPixel()
            
            if num_components > 1:
                # 彩色图像处理
                result_array = sitk.GetArrayFromImage(result)
                result_array = np.clip(result_array, 0, 255)
                result_8bit_array = result_array.astype(np.uint8)
                result_8bit = sitk.GetImageFromArray(result_8bit_array, isVector=True)
                result_8bit.SetSpacing(result.GetSpacing())
                result_8bit.SetOrigin(result.GetOrigin())
            else:
                # 灰度图像处理
                result_8bit = sitk.Cast(sitk.RescaleIntensity(result), sitk.sitkUInt8)
            
            sitk.WriteImage(result_8bit, str(output_path))
            print(f"拼接结果已保存到: {output_path}")
            return True
            
        except Exception as e:
            print(f"保存结果时出错: {e}")
            return False
    
    def run_stitching(self) -> Optional[sitk.Image]:
        """
        执行完整的拼接流程
        
        Returns:
            Optional[sitk.Image]: 拼接结果或None
        """
        try:
            # 1. 加载图像
            if not self.load_images():
                return None
            
            # 2. 配准图像
            cumulative_shifts = self.register_images()
            if not cumulative_shifts:
                print("配准失败")
                return None
            
            # 3. 拼接图像
            result = self.stitch_images_with_blending(cumulative_shifts)
            if result is None:
                return None
            
            # 4. 保存结果
            self.save_result(result)
            
            print("图像拼接完成!")
            print(f"结果图像尺寸: {result.GetSize()}")
            return result
            
        except Exception as e:
            print(f"拼接流程出错: {e}")
            import traceback
            traceback.print_exc()
            return None

# 使用示例
if __name__ == "__main__":
    # 测试test02文件夹 - 6行16列
    image_folder = r"test02"
    
    # 创建拼接器实例 - 指定6行16列
    stitcher = ImageStitcher(image_folder, grid_rows=6, grid_cols=16)
    
    # 执行拼接
    result = stitcher.run_stitching()
    
    if result is not None:
        print("图像拼接完成!")
        print(f"结果图像尺寸: {result.GetSize()}")
    else:
        print("图像拼接失败!")