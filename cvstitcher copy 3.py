import cv2
import numpy as np
import os
from scipy.optimize import least_squares

class CVStitcher:
    def __init__(self, image_dir, rows=3, cols=3, overlap_ratio=0.1):
        self.image_dir = image_dir
        self.rows = rows
        self.cols = cols
        self.overlap_ratio = overlap_ratio
        self.images = {}
        self.image_positions = {}
        self.global_offsets = {}
        
    def load_images(self):
        """加载所有图片"""
        for r in range(self.rows):
            for c in range(self.cols):
                filename = f"r{r:03d}_c{c:03d}.jpg"
                filepath = os.path.join(self.image_dir, filename)
                if os.path.exists(filepath):
                    img = cv2.imread(filepath, cv2.IMREAD_GRAYSCALE)
                    if img is not None:
                        self.images[(r, c)] = img
                        print(f"Loaded image: {filename}, shape: {img.shape}")
                    else:
                        print(f"Failed to load: {filename}")
                else:
                    print(f"File not found: {filename}")
    
    def apply_hanning_window(self, img):
        """应用汉宁窗以减少边界效应"""
        h, w = img.shape
        hann_h = np.hanning(h).reshape(-1, 1)
        hann_w = np.hanning(w).reshape(1, -1)
        hann_2d = hann_h * hann_w
        return img.astype(np.float32) * hann_2d
    
    def subpixel_peak_refinement(self, correlation_map):
        """亚像素峰值精化"""
        # 找到整数峰值位置
        peak_y, peak_x = np.unravel_index(np.argmax(correlation_map), correlation_map.shape)
        h, w = correlation_map.shape
        
        # 检查边界
        if peak_x == 0 or peak_x == w-1 or peak_y == 0 or peak_y == h-1:
            return peak_x, peak_y
        
        # 使用抛物线拟合进行亚像素精化
        # X方向的亚像素精化
        c1 = correlation_map[peak_y, peak_x-1]
        c2 = correlation_map[peak_y, peak_x]
        c3 = correlation_map[peak_y, peak_x+1]
        
        if c1 + c3 - 2*c2 != 0:
            dx = 0.5 * (c1 - c3) / (c1 + c3 - 2*c2)
        else:
            dx = 0
        
        # Y方向的亚像素精化
        c1 = correlation_map[peak_y-1, peak_x]
        c2 = correlation_map[peak_y, peak_x]
        c3 = correlation_map[peak_y+1, peak_x]
        
        if c1 + c3 - 2*c2 != 0:
            dy = 0.5 * (c1 - c3) / (c1 + c3 - 2*c2)
        else:
            dy = 0
        
        return peak_x + dx, peak_y + dy
    
    def enhanced_phase_correlation(self, img1, img2, use_hanning=True):
        """增强版相位相关，提供亚像素精度"""
        # 确保图片大小相同
        h1, w1 = img1.shape
        h2, w2 = img2.shape
        h = min(h1, h2)
        w = min(w1, w2)
        
        img1_crop = img1[:h, :w]
        img2_crop = img2[:h, :w]
        
        # 应用汉宁窗
        if use_hanning:
            img1_windowed = self.apply_hanning_window(img1_crop)
            img2_windowed = self.apply_hanning_window(img2_crop)
        else:
            img1_windowed = img1_crop.astype(np.float32)
            img2_windowed = img2_crop.astype(np.float32)
        
        # 计算FFT
        f1 = np.fft.fft2(img1_windowed)
        f2 = np.fft.fft2(img2_windowed)
        
        # 计算交叉功率谱
        cross_power_spectrum = f1 * np.conj(f2)
        cross_power_spectrum /= (np.abs(cross_power_spectrum) + 1e-10)
        
        # 计算相位相关
        correlation = np.real(np.fft.ifft2(cross_power_spectrum))
        
        # 亚像素峰值精化
        peak_x_sub, peak_y_sub = self.subpixel_peak_refinement(correlation)
        
        # 正确处理周期性偏移 - 修正符号问题
        # 如果峰值在后半部分，说明实际偏移是负的
        if peak_y_sub > h // 2:
            peak_y_sub = peak_y_sub - h
        if peak_x_sub > w // 2:
            peak_x_sub = peak_x_sub - w
        
        # 计算置信度（使用峰值和其邻域的比值）
        max_val = np.max(correlation)
        mean_val = np.mean(correlation)
        confidence = (max_val - mean_val) / (max_val + 1e-10)
        
        # 返回偏移量：img2相对于img1的偏移
        # 相位相关直接给出的就是img2相对于img1的偏移，不需要取负号
        return peak_x_sub, peak_y_sub, confidence
    
    def get_optimal_overlap_roi(self, img1, img2, direction):
        """获取最优重叠区域"""
        h1, w1 = img1.shape
        h2, w2 = img2.shape
        
        # 从ImageJ日志推断实际重叠度
        if direction == 'horizontal':
            # ImageJ显示水平步长约2181，图片宽度2448
            estimated_overlap = (2448 - 2181) / 2448
            overlap_width = int(w1 * estimated_overlap)
            # 确保重叠区域不会太小
            overlap_width = max(overlap_width, 200)
            roi1 = img1[:, -overlap_width:]
            roi2 = img2[:, :overlap_width]
        elif direction == 'vertical':
            # 垂直方向使用类似的逻辑
            estimated_overlap = (2048 - 1830) / 2048  # 从日志估算
            overlap_height = int(h1 * estimated_overlap)
            overlap_height = max(overlap_height, 200)
            roi1 = img1[-overlap_height:, :]
            roi2 = img2[:overlap_height, :]
        else:
            raise ValueError("Direction must be 'horizontal' or 'vertical'")
        
        return roi1, roi2
    
    def calculate_pairwise_offsets(self):
        """计算相邻图片之间的偏移"""
        pairwise_offsets = {}
        
        # 计算水平方向的偏移（同一行，相邻列）
        for r in range(self.rows):
            for c in range(self.cols - 1):
                if (r, c) in self.images and (r, c + 1) in self.images:
                    img1 = self.images[(r, c)]
                    img2 = self.images[(r, c + 1)]
                    
                    print(f"Processing horizontal pair ({r},{c}) -> ({r},{c+1})")
                    roi1, roi2 = self.get_optimal_overlap_roi(img1, img2, 'horizontal')
                    dx, dy, confidence = self.enhanced_phase_correlation(roi1, roi2)
                    
                    # 调整偏移量（考虑ROI位置）
                    # 水平方向：actual_dx = 非重叠部分宽度 + ROI内的偏移
                    actual_dx = img1.shape[1] - roi1.shape[1] + dx
                    actual_dy = dy
                    
                    pairwise_offsets[((r, c), (r, c + 1))] = (actual_dx, actual_dy, confidence)
                    print(f"  Result: dx={actual_dx:.4f}, dy={actual_dy:.4f}, conf={confidence:.6f}")
        
        # 计算垂直方向的偏移（同一列，相邻行）
        for r in range(self.rows - 1):
            for c in range(self.cols):
                if (r, c) in self.images and (r + 1, c) in self.images:
                    img1 = self.images[(r, c)]
                    img2 = self.images[(r + 1, c)]
                    
                    print(f"Processing vertical pair ({r},{c}) -> ({r+1},{c})")
                    roi1, roi2 = self.get_optimal_overlap_roi(img1, img2, 'vertical')
                    dx, dy, confidence = self.enhanced_phase_correlation(roi1, roi2)
                    
                    # 调整偏移量（考虑ROI位置）
                    # 垂直方向：actual_dy = 非重叠部分高度 + ROI内的偏移
                    actual_dx = dx
                    actual_dy = img1.shape[0] - roi1.shape[0] + dy
                    
                    pairwise_offsets[((r, c), (r + 1, c))] = (actual_dx, actual_dy, confidence)
                    print(f"  Result: dx={actual_dx:.4f}, dy={actual_dy:.4f}, conf={confidence:.6f}")
        
        return pairwise_offsets
    
    def global_optimization(self, pairwise_offsets):
        """使用全局优化计算位置，减少累积误差"""
        # 创建变量索引
        tile_to_idx = {}
        idx_to_tile = {}
        idx = 0
        for r in range(self.rows):
            for c in range(self.cols):
                if (r, c) in self.images:
                    tile_to_idx[(r, c)] = idx
                    idx_to_tile[idx] = (r, c)
                    idx += 1
        
        num_tiles = len(tile_to_idx)
        
        # 准备约束数据
        constraints = []
        weights = []
        
        for pair, (dx, dy, confidence) in pairwise_offsets.items():
            tile1, tile2 = pair
            if tile1 in tile_to_idx and tile2 in tile_to_idx:
                idx1 = tile_to_idx[tile1]
                idx2 = tile_to_idx[tile2]
                constraints.append((idx1, idx2, dx, dy))
                weights.append(confidence)
        
        # 初始位置估计（简单累加结果）
        initial_positions = {(0, 0): (0, 0)}
        for c in range(1, self.cols):
            if ((0, c-1), (0, c)) in pairwise_offsets:
                prev_x, prev_y = initial_positions[(0, c-1)]
                dx, dy, _ = pairwise_offsets[((0, c-1), (0, c))]
                initial_positions[(0, c)] = (prev_x + dx, prev_y + dy)
        
        for r in range(1, self.rows):
            for c in range(self.cols):
                if ((r-1, c), (r, c)) in pairwise_offsets:
                    prev_x, prev_y = initial_positions[(r-1, c)]
                    dx, dy, _ = pairwise_offsets[((r-1, c), (r, c))]
                    initial_positions[(r, c)] = (prev_x + dx, prev_y + dy)
        
        # 构建初始参数向量
        x0 = np.zeros(2 * num_tiles)
        for tile, (x, y) in initial_positions.items():
            if tile in tile_to_idx:
                idx = tile_to_idx[tile]
                x0[2*idx] = x
                x0[2*idx + 1] = y
        
        # 定义残差函数
        def residuals(params):
            residual_list = []
            for i, (idx1, idx2, dx_obs, dy_obs) in enumerate(constraints):
                x1, y1 = params[2*idx1], params[2*idx1 + 1]
                x2, y2 = params[2*idx2], params[2*idx2 + 1]
                
                dx_pred = x2 - x1
                dy_pred = y2 - y1
                
                weight = weights[i]
                residual_list.append(weight * (dx_pred - dx_obs))
                residual_list.append(weight * (dy_pred - dy_obs))
            
            return np.array(residual_list)
        
        # 固定第一个图像位置为(0,0)
        def residuals_with_constraint(params):
            # 强制第一个图像位置为(0,0)
            params[0] = 0  # x坐标
            params[1] = 0  # y坐标
            return residuals(params)
        
        # 执行优化
        print("\nPerforming global optimization...")
        result = least_squares(residuals_with_constraint, x0, method='lm')
        
        if result.success:
            print("Global optimization converged successfully")
        else:
            print("Global optimization failed, using initial solution")
            result.x = x0
        
        # 提取优化后的位置
        optimized_positions = {}
        for idx in range(num_tiles):
            tile = idx_to_tile[idx]
            x_opt = result.x[2*idx]
            y_opt = result.x[2*idx + 1]
            optimized_positions[tile] = (x_opt, y_opt)
        
        return optimized_positions
    
    def calculate_global_positions(self, pairwise_offsets):
        """计算全局绝对位置 - 使用全局优化"""
        return self.global_optimization(pairwise_offsets)
    
    def load_imagej_results(self, filepath):
        """加载ImageJ的配准结果"""
        imagej_positions = {}
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('r') and '.jpg' in line:
                        parts = line.split(';')
                        filename = parts[0].strip()
                        coords_str = parts[2].strip()
                        # 解析坐标 "(x, y)"
                        coords_str = coords_str.strip('()')
                        x, y = map(float, coords_str.split(','))
                        
                        # 从文件名提取行列信息
                        basename = filename.replace('.jpg', '')
                        r = int(basename.split('_')[0][1:])
                        c = int(basename.split('_')[1][1:])
                        
                        imagej_positions[(r, c)] = (x, y)
            print(f"Loaded ImageJ results: {len(imagej_positions)} positions")
        return imagej_positions
    
    def compare_with_imagej(self, our_positions, imagej_positions):
        """比较我们的结果与ImageJ的结果"""
        print("\n" + "="*80)
        print("COMPARISON WITH IMAGEJ RESULTS (Fixed Coordinate System)")
        print("="*80)
        print(f"{'Position':<12} {'Our Result':<25} {'ImageJ Result':<25} {'Difference':<15}")
        print("-" * 80)
        
        total_diff = 0
        count = 0
        
        for (r, c) in sorted(our_positions.keys()):
            if (r, c) in imagej_positions:
                our_x, our_y = our_positions[(r, c)]
                ij_x, ij_y = imagej_positions[(r, c)]
                
                diff_x = our_x - ij_x
                diff_y = our_y - ij_y
                diff_magnitude = np.sqrt(diff_x**2 + diff_y**2)
                
                print(f"({r},{c}):      ({our_x:10.4f}, {our_y:10.4f})   ({ij_x:10.4f}, {ij_y:10.4f})   {diff_magnitude:8.4f}")
                
                total_diff += diff_magnitude
                count += 1
        
        if count > 0:
            avg_diff = total_diff / count
            print("-" * 80)
            print(f"Average difference: {avg_diff:.4f} pixels")
    
    def save_configuration(self, positions, filename="TileConfiguration_opencv_fixed.txt"):
        """保存配置文件"""
        with open(filename, 'w') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n\n")
            f.write("# Define the image coordinates\n")
            
            for (r, c), (x, y) in positions.items():
                if (r, c) in self.images:
                    img_name = f"r{r:03d}_c{c:03d}.jpg"
                    f.write(f"{img_name}; ; ({x:.6f}, {y:.6f})\n")
        
        print(f"Configuration saved to {filename}")
    
    def run_stitching(self):
        """执行完整的拼接流程"""
        print("Loading images...")
        self.load_images()
        
        if not self.images:
            print("No images loaded!")
            return
        
        print(f"Loaded {len(self.images)} images")
        
        print("\nCalculating pairwise offsets with enhanced phase correlation...")
        pairwise_offsets = self.calculate_pairwise_offsets()
        
        print("\nCalculating global positions with optimization...")
        positions = self.calculate_global_positions(pairwise_offsets)
        
        print("\nFinal optimized positions:")
        for (r, c), (x, y) in sorted(positions.items()):
            print(f"Image ({r},{c}): position ({x:.6f}, {y:.6f})")
        
        # 保存配置文件
        self.save_configuration(positions)
        
        # 加载并比较ImageJ结果
        imagej_file = os.path.join(self.image_dir, "TileConfiguration.registered-imagej.txt")
        imagej_positions = self.load_imagej_results(imagej_file)
        if imagej_positions:
            self.compare_with_imagej(positions, imagej_positions)
        
        return positions

def main():
    # 设置参数
    image_dir = "test02"
    rows = 6
    cols = 16
    overlap_ratio = 0.1  # 初始估计
    
    # 创建拼接器
    stitcher = CVStitcher(image_dir, rows, cols, overlap_ratio)
    
    # 执行拼接
    positions = stitcher.run_stitching()
    
    if positions:
        print("\nStitching completed successfully!")
    else:
        print("\nStitching failed!")

if __name__ == "__main__":
    main()
